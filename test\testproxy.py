import httpx
import asyncio
from httpx_socks import AsyncProxyTransport
import os

async def test_proxy_connection():
    proxy_url = "socks5://*************:44444"
    transport = AsyncProxyTransport.from_url(proxy_url)

    try:
        async with httpx.AsyncClient(mounts={"all://": transport}, timeout=10) as client:
            response = await client.get("https://httpbin.org/ip")
            response.raise_for_status()
            print("代理请求成功！")
            print("请求源IP:", response.json().get('origin'))
    except Exception as e:
        print(f"代理请求失败，错误类型: {type(e).__name__} - {e}")

if __name__ == "__main__":
    asyncio.run(test_proxy_connection())