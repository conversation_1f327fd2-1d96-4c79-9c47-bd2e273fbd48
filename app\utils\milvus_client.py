"""
Milvus直连客户端
实现真实的混合搜索功能
"""

import re
import asyncio
from typing import Dict, List, Any, Optional
from loguru import logger

try:
    from pymilvus import (
        connections, 
        Collection, 
        FieldSchema, 
        CollectionSchema, 
        DataType,
        utility
    )
    PYMILVUS_AVAILABLE = True
except ImportError:
    logger.warning("pymilvus未安装，将使用mock模式")
    PYMILVUS_AVAILABLE = False

from app.config.settings import settings


class MilvusDirectClient:
    """Milvus直连客户端"""
    
    def __init__(self):
        self.host = settings.milvus_host
        self.port = settings.milvus_port
        self.collection_name = getattr(settings, 'milvus_collection_name', 'knowledge_base')
        self.connection_alias = "default"
        self.connected = False
        self.collection = None
    
    async def connect(self):
        """连接到Milvus"""
        try:
            if not PYMILVUS_AVAILABLE:
                logger.warning("pymilvus未安装，使用mock连接")
                self.connected = True
                return True
                
            # 连接到Milvus
            connections.connect(
                alias=self.connection_alias,
                host=self.host,
                port=self.port
            )
            
            logger.info(f"成功连接到Milvus: {self.host}:{self.port}")
            self.connected = True
            
            # 检查集合是否存在
            if utility.has_collection(self.collection_name):
                self.collection = Collection(self.collection_name)
                logger.info(f"找到集合: {self.collection_name}")
            else:
                logger.warning(f"集合 {self.collection_name} 不存在")
                
            return True
            
        except Exception as e:
            logger.error(f"连接Milvus失败: {str(e)}")
            self.connected = False
            return False
    
    async def hybrid_search(
        self, 
        query_text: str, 
        knowledge_base_id: str,
        search_params: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """混合搜索：向量搜索 + 关键词搜索"""
        try:
            # 默认搜索参数
            default_params = {
                "top_k": 5,
                "similarity_threshold": 0.7,
                "rerank_top_k": 10,
                "vector_weight": 0.7,
                "keyword_weight": 0.3
            }
            
            if search_params:
                default_params.update(search_params)
            
            logger.info(f"执行混合搜索: query={query_text}, kb_id={knowledge_base_id}, params={default_params}")
            
            if not self.connected:
                await self.connect()
            
            if not PYMILVUS_AVAILABLE or not self.collection:
                return await self._mock_search(query_text, knowledge_base_id, default_params)
            
            # 执行真实的混合搜索
            return await self._real_hybrid_search(query_text, knowledge_base_id, default_params)
            
        except Exception as e:
            logger.error(f"混合搜索失败: {str(e)}")
            return await self._mock_search(query_text, knowledge_base_id, default_params)
    
    async def _real_hybrid_search(
        self, 
        query_text: str, 
        knowledge_base_id: str,
        search_params: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """执行真实的混合搜索"""
        try:
            # 1. 向量搜索
            vector_results = await self._vector_search(query_text, knowledge_base_id, search_params)
            
            # 2. 关键词搜索
            keyword_results = await self._keyword_search(query_text, knowledge_base_id, search_params)
            
            # 3. 混合排序和重排序
            hybrid_results = self._merge_and_rerank(
                vector_results, 
                keyword_results, 
                search_params
            )
            
            # 4. 应用相似度阈值过滤
            filtered_results = [
                result for result in hybrid_results 
                if result.get("score", 0) >= search_params.get("similarity_threshold", 0.7)
            ]
            
            # 5. 返回top_k结果
            top_k = search_params.get("top_k", 5)
            final_results = filtered_results[:top_k]
            
            logger.info(f"混合搜索返回{len(final_results)}个结果")
            return final_results
            
        except Exception as e:
            logger.error(f"真实混合搜索失败: {str(e)}")
            return []
    
    async def _vector_search(
        self, 
        query_text: str, 
        knowledge_base_id: str,
        search_params: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """向量搜索"""
        try:
            # 这里需要将query_text转换为向量
            # 实际实现中需要调用embedding模型
            query_vector = await self._text_to_vector(query_text)
            
            # 构建搜索表达式，过滤特定知识库
            expr = f"knowledge_base_id == '{knowledge_base_id}'"
            
            # 执行向量搜索
            search_params_milvus = {
                "metric_type": "COSINE",
                "params": {"nprobe": 10}
            }
            
            results = self.collection.search(
                data=[query_vector],
                anns_field="embedding",
                param=search_params_milvus,
                limit=search_params.get("rerank_top_k", 10),
                expr=expr,
                output_fields=["content", "metadata", "knowledge_base_id"]
            )
            
            # 转换结果格式
            vector_results = []
            for hits in results:
                for hit in hits:
                    vector_results.append({
                        "id": str(hit.id),
                        "content": hit.entity.get("content", ""),
                        "score": float(hit.score),
                        "metadata": hit.entity.get("metadata", {}),
                        "search_type": "vector"
                    })
            
            logger.info(f"向量搜索返回{len(vector_results)}个结果")
            return vector_results
            
        except Exception as e:
            logger.error(f"向量搜索失败: {str(e)}")
            return []
    
    async def _keyword_search(
        self, 
        query_text: str, 
        knowledge_base_id: str,
        search_params: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """关键词搜索"""
        try:
            # 提取关键词
            keywords = self._extract_keywords(query_text)
            
            # 构建关键词搜索表达式
            keyword_exprs = []
            for keyword in keywords:
                keyword_exprs.append(f"content like '%{keyword}%'")
            
            if keyword_exprs:
                keyword_expr = " or ".join(keyword_exprs)
                expr = f"knowledge_base_id == '{knowledge_base_id}' and ({keyword_expr})"
            else:
                expr = f"knowledge_base_id == '{knowledge_base_id}'"
            
            # 执行查询
            results = self.collection.query(
                expr=expr,
                output_fields=["content", "metadata", "knowledge_base_id"],
                limit=search_params.get("rerank_top_k", 10)
            )
            
            # 计算关键词匹配分数
            keyword_results = []
            for result in results:
                score = self._calculate_keyword_score(query_text, result.get("content", ""))
                keyword_results.append({
                    "id": str(result.get("id", "")),
                    "content": result.get("content", ""),
                    "score": score,
                    "metadata": result.get("metadata", {}),
                    "search_type": "keyword"
                })
            
            # 按分数排序
            keyword_results.sort(key=lambda x: x["score"], reverse=True)
            
            logger.info(f"关键词搜索返回{len(keyword_results)}个结果")
            return keyword_results
            
        except Exception as e:
            logger.error(f"关键词搜索失败: {str(e)}")
            return []
    
    async def _text_to_vector(self, text: str) -> List[float]:
        """将文本转换为向量（需要embedding模型）"""
        # 这里需要调用实际的embedding模型
        # 暂时返回随机向量作为示例
        import random
        vector_dim = 768  # 假设向量维度为768
        return [random.random() for _ in range(vector_dim)]
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取
        # 移除标点符号，分词
        text = re.sub(r'[^\w\s]', ' ', text)
        words = text.split()
        
        # 过滤停用词和短词
        stop_words = {'的', '是', '在', '有', '和', '与', '或', '但', '如何', '什么', '怎么'}
        keywords = [word for word in words if len(word) > 1 and word not in stop_words]
        
        return keywords[:10]  # 最多返回10个关键词
    
    def _calculate_keyword_score(self, query: str, content: str) -> float:
        """计算关键词匹配分数"""
        query_keywords = set(self._extract_keywords(query.lower()))
        content_keywords = set(self._extract_keywords(content.lower()))
        
        if not query_keywords:
            return 0.0
        
        # 计算交集比例
        intersection = query_keywords.intersection(content_keywords)
        score = len(intersection) / len(query_keywords)
        
        return min(score, 1.0)
    
    def _merge_and_rerank(
        self, 
        vector_results: List[Dict[str, Any]], 
        keyword_results: List[Dict[str, Any]],
        search_params: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """合并和重排序结果"""
        vector_weight = search_params.get("vector_weight", 0.7)
        keyword_weight = search_params.get("keyword_weight", 0.3)
        
        # 创建结果字典，以ID为键
        merged_results = {}
        
        # 添加向量搜索结果
        for result in vector_results:
            doc_id = result["id"]
            merged_results[doc_id] = result.copy()
            merged_results[doc_id]["vector_score"] = result["score"]
            merged_results[doc_id]["keyword_score"] = 0.0
        
        # 添加关键词搜索结果
        for result in keyword_results:
            doc_id = result["id"]
            if doc_id in merged_results:
                merged_results[doc_id]["keyword_score"] = result["score"]
            else:
                merged_results[doc_id] = result.copy()
                merged_results[doc_id]["vector_score"] = 0.0
                merged_results[doc_id]["keyword_score"] = result["score"]
        
        # 计算混合分数
        final_results = []
        for doc_id, result in merged_results.items():
            hybrid_score = (
                result["vector_score"] * vector_weight + 
                result["keyword_score"] * keyword_weight
            )
            result["score"] = hybrid_score
            final_results.append(result)
        
        # 按混合分数排序
        final_results.sort(key=lambda x: x["score"], reverse=True)
        
        return final_results
    
    async def _mock_search(
        self, 
        query_text: str, 
        knowledge_base_id: str,
        search_params: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Mock搜索结果"""
        mock_results = [
            {
                "id": "doc_001",
                "content": f"关于{query_text}的配置方法和详细步骤...",
                "score": 0.95,
                "metadata": {
                    "knowledge_base_id": knowledge_base_id,
                    "document_type": "config_guide",
                    "vendor": "华为",
                    "device": "路由器"
                }
            },
            {
                "id": "doc_002", 
                "content": f"{query_text}的详细配置步骤和最佳实践...",
                "score": 0.88,
                "metadata": {
                    "knowledge_base_id": knowledge_base_id,
                    "document_type": "step_guide",
                    "vendor": "华为",
                    "device": "路由器"
                }
            }
        ]
        
        # 应用阈值过滤
        threshold = search_params.get("similarity_threshold", 0.7)
        filtered_results = [r for r in mock_results if r["score"] >= threshold]
        
        # 返回top_k结果
        top_k = search_params.get("top_k", 5)
        return filtered_results[:top_k]
    
    async def close(self):
        """关闭连接"""
        try:
            if self.connected and PYMILVUS_AVAILABLE:
                connections.disconnect(self.connection_alias)
                self.connected = False
                logger.info("关闭Milvus连接")
        except Exception as e:
            logger.error(f"关闭Milvus连接失败: {str(e)}")


# 全局Milvus客户端实例
milvus_client = MilvusDirectClient()
