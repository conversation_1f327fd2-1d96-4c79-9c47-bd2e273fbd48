"""
定制化能力模块
基于Excel文本和Jinja2模板生成配置
"""

import json
from typing import Dict, Any, List, Optional, AsyncGenerator
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, Template
from loguru import logger
import ipaddress

from app.config.settings import settings
from app.models.request import CustomizedCapabilityRequest
from app.models.response import StreamResponse
from app.utils.text_converter import text_converter
from app.core.base_capability import BaseCapability


class CustomizedCapability(BaseCapability):
    """定制化能力类"""

    def __init__(self):
        super().__init__("customized-capability")
        self.template_dir = Path(settings.template_dir)
        self.template_dir.mkdir(exist_ok=True)
        
        # 初始化Jinja2环境
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
    def convertVarableForJinja2(self,rawExcelData:Dict):
        
        def cidr_to_ip_mask(ip_cidr):
            ip_interface = ipaddress.IPv4Interface(ip_cidr)
            return str(ip_interface.ip), str(ip_interface.netmask)    
        # 解析字段
        info_notinside_excel={
            'fw_ip_cidr':'***********/30',
            'fw_interface':'Eth-Trunk10'
        }
        ce_name = (rawExcelData.get('CE端设备') or '').strip()
        ce_port = (rawExcelData.get('CE端口') or '').strip()
        ce_ip_cidr = (rawExcelData.get('CE端互联IP地址') or '').strip()
        vlan = (rawExcelData.get('VLAN') or '').strip()
        bfd_str = (rawExcelData.get('BFD时延') or '').strip()
        bfd_interval = ''.join(filter(str.isdigit, bfd_str))  # 提取数字部分
        vpn_instance = (rawExcelData.get('vpn-instance') or '').strip()
        rd_rt = (rawExcelData.get('rt/rd') or '').strip()
        in_prefix = (rawExcelData.get('接收地址范围') or '').strip().replace('\n', ' ')
        in_prefix = in_prefix.split('最小接受')[0].strip()  # 只取前缀部分
        out_prefix = (rawExcelData.get('发送地址范围') or '').strip().split('\n')[0].strip()
        bgp_as = (rawExcelData.get('CE端BGP AS号') or '').strip()
        er_bgp_as = (rawExcelData.get('AS号') or '').strip() 
        er_name = (rawExcelData.get('终端设备') or '').strip()
        er_interface = (rawExcelData.get('终端端口') or '').strip() 
        er_ip_cidr = (rawExcelData.get('ER互联IP地址') or '').strip()
        fw_ip_cidr = (rawExcelData.get('防火墙互联IP地址', info_notinside_excel.get('fw_ip_cidr') or '') or '').strip()  # 可选，支持用户自定义
        fw_bgp_as = bgp_as  # 假设与 CE 的 AS 相同
        fw_name = "FW"
        # 解析 IP 地址
        er_ip, er_mask = cidr_to_ip_mask(ce_ip_cidr)
        fw_ip, fw_mask = cidr_to_ip_mask(fw_ip_cidr)
        neighbor_er_ip = er_ip_cidr.split('/')[0] if '/' in er_ip_cidr else er_ip_cidr.split('/')[0]
        # 构建 BGP 邻居列表
        bgp_peers = []
        # 添加 ER 邻居
        if neighbor_er_ip and er_bgp_as:
            bgp_peers.append({
                "ip": neighbor_er_ip,
                "as_number": er_bgp_as,
                "description": f"peer_to_{er_name}",
                "import_filter": f"pl_{vpn_instance}_in",
                "export_filter": f"pl_{vpn_instance}_out"
            })
        # 添加 FW 邻居（通常使用本设备 CE 的 AS）
        fw_neighbor_ip = fw_ip_cidr.split('/')[0]
        bgp_peers.append({
            "ip": fw_neighbor_ip,
            "as_number": fw_bgp_as,
            "description": f"peer_to_{fw_name}",
            "next_hop_local": True
        })
        # 渲染模板
        return {
            'bgp_as':bgp_as,
            'vpn_instance':vpn_instance,
            'rd_rt':rd_rt,
            'bfd_interval':bfd_interval,
            'bgp_peers':bgp_peers,
            'ce_name':ce_name,
            'ce_interface':ce_port,
            'vlan':vlan,
            'er_ip':er_ip,
            'er_mask':er_mask,
            'fw_ip':fw_ip,
            'fw_mask':fw_mask,
            'in_prefix':in_prefix,
            'out_prefix':out_prefix,
            'er_interface':ce_port,
            'fw_interface':info_notinside_excel['fw_interface']  # 示例接口名，你可以用 Excel 字段替代
        }
    
    def _parse_excel_content(self, excel_content: str) -> List[Dict[str, Any]]:
        """解析Excel文本内容为结构化数据"""
        try:
            lines = excel_content.strip().split('\n')
            if not lines:
                return []
            
            # 第一行作为表头
            headers = [h.strip() for h in lines[0].split('|')]
            data_rows = []
            
            # 解析数据行
            for line in lines[1:]:
                if line.strip():
                    values = [v.strip() for v in line.split('|')]
                    # 确保值的数量与表头匹配
                    while len(values) < len(headers):
                        values.append('')
                    
                    row_data = dict(zip(headers, values[:len(headers)]))
                    data_rows.append(row_data)
            
            logger.info(f"解析Excel内容成功，表头: {headers}, 数据行数: {len(data_rows)}")
            return data_rows
            
        except Exception as e:
            logger.error(f"解析Excel内容失败: {str(e)}")
            raise ValueError(f"Excel内容解析失败: {str(e)}")
    
    def _get_template_name(self, device_type: str, vendor: str) -> str:
        """根据设备类型和厂商获取模板名称"""
        # 模板命名规则: config-vpn-{vendor}-{device_type}.j2
        template_name = f"config-vpn-{vendor}-{device_type}.j2"
        
        # 检查模板是否存在
        template_path = self.template_dir / template_name
        if template_path.exists():
            return template_name
        
        # 如果特定模板不存在，尝试通用模板
        generic_template = f"config-vpn-common.j2"
        generic_path = self.template_dir / generic_template
        if generic_path.exists():
            return generic_template
        
        # 如果都不存在，返回默认模板
        default_template = "config-vpn-default.j2"
        return default_template
    
    def _create_default_template(self) -> str:
        """创建默认模板"""
        default_template_content = """
# 配置生成时间: {{ generation_time }}
# 设备类型: {{ device_type }}
# 厂商: {{ vendor }}

{% for item in data_items %}
# 配置项 {{ loop.index }}
{% for key, value in item.items() %}
# {{ key }}: {{ value }}
{% endfor %}

{% endfor %}
"""
        
        default_template_path = self.template_dir / "config-vpn-default.j2"
        with open(default_template_path, 'w', encoding='utf-8') as f:
            f.write(default_template_content)
        
        return "config-vpn-default.j2"
    
    def _render_template(
        self, 
        template_name: str, 
        single_config_data: Dict[str, Any]
    ) -> str:
        """渲染Jinja2模板"""
        try:
            template = self.jinja_env.get_template(template_name)
            print(single_config_data)
            
            rendered_config = template.render(**(self.convertVarableForJinja2(single_config_data)))
            return rendered_config.strip()
            
        except Exception as e:
            logger.error(f"模板渲染失败: {str(e)}")
            # 如果模板渲染失败，创建并使用默认模板
            default_template = self._create_default_template()
            template = self.jinja_env.get_template(default_template)
            return template.render()
    
    def _extract_excel_data_from_messages(self, messages: list) -> List[Dict[str, Any]]:
        """从消息列表中直接提取Excel数据（优先使用attachment字段）"""
        # 从后往前查找最新的用户消息
        for message in reversed(messages):
            if message.role == "user":
                # 优先从attachment字段获取Excel数据
                if hasattr(message, 'attachment') and message.attachment is not None:
                    # 如果attachment是列表（Excel解析后的数据），直接返回 - 这是我们想要的！
                    if isinstance(message.attachment, list):
                        logger.info(f"从attachment字段获取Excel结构化数据，数据行数: {len(message.attachment)}")
                        return message.attachment
                    # 如果attachment是字符串（TXT文件），这不适用于customize场景
                    elif isinstance(message.attachment, str):
                        logger.warning("customize场景收到TXT格式attachment，尝试解析为Excel格式")
                        return self._parse_excel_content(message.attachment)

                # 向后兼容：如果没有attachment字段，从content解析
                logger.info("未找到attachment字段，从content字段解析Excel内容")
                return self._parse_excel_content(message.content.strip())

        return []

    def _prepare_generation_data(self, request: CustomizedCapabilityRequest) -> Dict[str, Any]:
        """准备配置生成的数据"""
        # 优先从attachment字段直接获取Excel数据
        data_items = self._extract_excel_data_from_messages(request.messages)

        if not data_items:
            raise ValueError("Excel内容为空或格式不正确")
        
        print(json.dumps(data_items,indent=4))
        # 获取模板名称
        template_name = self._get_template_name(request.device_type, request.vendor)
        print(template_name)

        # 准备模板数据
        template_data = {
            "data_items": data_items,
            "device_type": request.device_type,
            "vendor": request.vendor,
            "generation_time": "{{ now() }}",
            "total_items": len(data_items)
        }

        return {
            "template_name": template_name,
            "template_data": template_data,
            "data_items": data_items
        }

    def _generate_config_content(self, generation_data: Dict[str, Any]) -> str:
        """生成配置内容"""
        template_name = generation_data["template_name"]
        template_data = generation_data["template_data"]

        # 渲染配置
        config_content="\n```python \n"
        for index,single_config_data in enumerate(template_data['data_items']):
            config_content += self._render_template(template_name, single_config_data)+"\n```\n"
            if index <len(template_data['data_items'])-1:
                config_content += "\n```python\n"
        logger.info(f"定制化配置生成完成，使用模板: {template_name}")
        return config_content

    def _create_content_chunks(self, content: str, chunk_size: int = 100) -> AsyncGenerator[Dict[str, Any], None]:
        """将内容分块为流式响应"""
        async def chunk_generator():
            # 分块发送配置内容
            for i in range(0, len(content), chunk_size):
                chunk = content[i:i + chunk_size]

                yield {
                    "id": f"chatcmpl-customized-{i}",
                    "object": "chat.completion.chunk",
                    "created": 1677652288,
                    "model": "customized-capability",
                    "choices": [
                        {
                            "index": 0,
                            "delta": {"content": chunk},
                            "finish_reason": None
                        }
                    ]
                }

            # 发送结束标记
            yield {
                "id": "chatcmpl-customized-end",
                "object": "chat.completion.chunk",
                "created": 1677652288,
                "model": "customized-capability",
                "choices": [
                    {
                        "index": 0,
                        "delta": {},
                        "finish_reason": "stop"
                    }
                ]
            }

        return chunk_generator()

    async def generate_config_stream(
        self,
        request: CustomizedCapabilityRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成配置（流式返回）"""
        try:
            # 准备生成数据
            generation_data = self._prepare_generation_data(request)
            
            print(generation_data)
            # 生成配置内容
            config_content = self._generate_config_content(generation_data)

            # 返回流式响应
            async for chunk in self._create_content_chunks(config_content):
                yield chunk

        except Exception as e:
            logger.error(f"定制化配置生成失败: {str(e)}")
            yield self._create_error_chunk(f"配置生成失败: {str(e)}")



    async def generate_config(self, request: CustomizedCapabilityRequest) -> str:
        """生成配置（非流式返回）"""
        try:
            # 准备生成数据
            generation_data = self._prepare_generation_data(request)

            # 生成配置内容
            config_content = self._generate_config_content(generation_data)

            return config_content

        except Exception as e:
            logger.error(f"定制化配置生成失败: {str(e)}")
            raise
        
   
    
        
# 全局定制化能力实例
customized_capability = CustomizedCapability()
