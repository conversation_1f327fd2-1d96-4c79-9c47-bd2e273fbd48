"""
应用配置模块
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = Field(default="数通设备配置生成智能体", description="应用名称")
    app_version: str = Field(default="0.1.0", description="应用版本")
    debug: bool = Field(default=True, description="调试模式")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8000, description="服务器端口")
    workers: int = Field(default=16, description="工作进程数")
    
    # LLM配置
    llm_base_url: str = Field(
        # default="https://api.siliconflow.cn/v1/chat/completions",#硅基测试用
        default="http://************:10010/CUCCAI-llm-hub/chat/completions",
        description="LLM服务地址"
    )
    llm_api_key: str = Field(
        # default="sk-folldchiodblcqtkxyegqmznowiiobdxpzyylaihxeljavhy",#硅基测试用
        default="618149eb-d43e-4ddc-b406-b0c0e1efd281",
        description="LLM API密钥"
    )
    llm_model: str = Field(default="deepseek_v3_int8_vpc", description="LLM模型名称")

    # llm_model: str = Field(default="Qwen3-32B", description="LLM模型名称")
    # llm_model: str = Field(default="Qwen3-235B-A22B", description="LLM模型名称")
    # llm_model: str = Field(default="THUDM/GLM-Z1-9B-0414", description="LLM模型名称")#硅基测试用
    llm_timeout: int = Field(default=600, description="LLM请求超时时间(秒)")
    llm_max_retries: int = Field(default=3, description="LLM请求最大重试次数")
    
    # 知识库配置
    kb_base_url: str = Field(
        # default="http://localhost:8888/vector_search",
        default="http://************:10010/CUCCAI-intelligent-agent/vectorSearchApi/",
        description="知识库服务地址"
    )
    kb_app_id: str = Field(default="urdDUFiZhKrZi", description="知识库应用ID")
    kb_category_id: int = Field(default=153, description="知识库分类ID")
    kb_top_k: int = Field(default=5, description="知识库检索Top-K")
    kb_similarity: float = Field(default=0.4, description="知识库相似度阈值")
    kb_timeout: int = Field(default=30, description="知识库请求超时时间(秒)")
    
    # 文件配置
    upload_max_size: int = Field(default=10 * 1024 * 1024, description="上传文件最大大小(字节)")
    template_dir: str = Field(default="templates", description="模板目录")
    log_dir: str = Field(default="logs", description="日志目录")
    
    # 安全配置
    cors_origins: list[str] = Field(default=["*"], description="CORS允许的源")
    cors_methods: list[str] = Field(default=["*"], description="CORS允许的方法")
    cors_headers: list[str] = Field(default=["*"], description="CORS允许的头部")
    
    # 并发配置
    max_concurrent_requests: int = Field(default=100, description="最大并发请求数")
    rate_limit_per_minute: int = Field(default=60, description="每分钟请求限制")

    # 配置推荐相关配置
    conversation_rounds: int = Field(default=3, description="多轮对话保存轮数")
    default_kb_id: str = Field(default="default_kb", description="默认知识库ID")

    # Milvus配置
    milvus_host: str = Field(default="*************", description="Milvus服务器地址")
    milvus_port: int = Field(default=19530, description="Milvus服务器端口")
    milvus_collection_name: str = Field(default="config_docs", description="Milvus集合名称")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
settings = Settings()
