#!/usr/bin/env python3
"""
新的attachment字段使用示例
"""

import json
import asyncio
import httpx
from app.models.request import Message, ConfigGenerationRequest


async def example_direct_api_usage():
    """直接使用API的示例"""
    print("=== 直接API使用示例 ===")
    
    # 示例1：使用attachment字段的Excel数据
    excel_data = [
        {"BGP AS号": "65001", "CE端设备": "Router1", "设备型号": "NE40E", "接口": "GE0/0/1", "IP地址": "***********/30"},
        {"BGP AS号": "65002", "CE端设备": "Router2", "设备型号": "NE40E", "接口": "GE0/0/2", "IP地址": "***********/30"},
        {"BGP AS号": "65003", "CE端设备": "Router3", "设备型号": "NE40E", "接口": "GE0/0/3", "IP地址": "***********/30"}
    ]
    
    message_with_excel = Message(
        role="user",
        content="请根据这个Excel表格生成华为路由器的BGP配置",
        attachment=excel_data
    )
    
    request = ConfigGenerationRequest(
        messages=[message_with_excel],
        device_type="router",
        vendor="huawei",
        stream=False
    )
    
    print(f"请求数据: {json.dumps(request.model_dump(), ensure_ascii=False, indent=2)}")
    
    # 示例2：使用attachment字段的TXT数据
    txt_config = """
interface GigabitEthernet0/0/1
 ip address *********** ***************
 undo shutdown
#
bgp 65001
 router-id *******
 peer *********** as-number 65002
 #
 ipv4-family unicast
  peer *********** enable
"""
    
    message_with_txt = Message(
        role="user",
        content="请参考这个配置文件，生成类似的配置",
        attachment=txt_config
    )
    
    request_txt = ConfigGenerationRequest(
        messages=[message_with_txt],
        device_type="router", 
        vendor="huawei",
        stream=False
    )
    
    print(f"TXT请求数据: {json.dumps(request_txt.model_dump(), ensure_ascii=False, indent=2)}")


def example_customize_scenario():
    """customize场景的优势示例"""
    print("\n=== customize场景优势示例 ===")

    print("原有方式（文本解析）:")
    old_way = {
        "messages": [
            {"role": "user", "content": "BGP AS号|设备名|接口\n65001|Router1|GE0/0/1\n65002|Router2|GE0/0/2"}
        ],
        "device_type": "router",
        "vendor": "huawei"
    }
    print(f"  {json.dumps(old_way, ensure_ascii=False)}")

    print("\n新方式（结构化数据）:")
    new_way = {
        "messages": [
            {
                "role": "user",
                "content": "请根据这个Excel表格生成BGP配置",
                "attachment": [
                    {"BGP AS号": "65001", "设备名": "Router1", "接口": "GE0/0/1"},
                    {"BGP AS号": "65002", "设备名": "Router2", "接口": "GE0/0/2"}
                ]
            }
        ],
        "device_type": "router",
        "vendor": "huawei"
    }
    print(f"  {json.dumps(new_way, ensure_ascii=False, indent=2)}")

    print("\n优势对比:")
    print("  - 数据分离：用户需求 vs 表格数据")
    print("  - 类型安全：结构化数据，无需解析")
    print("  - 模板集成：直接用于Jinja2模板")
    print("  - 错误减少：避免文本解析错误")


def example_curl_commands():
    """生成curl命令示例"""
    print("\n=== cURL命令示例 ===")
    
    # 1. 传统方式（向后兼容）
    traditional_request = {
        "messages": [
            {"role": "user", "content": "BGP AS号|设备名|接口\n65001|Router1|GE0/0/1\n65002|Router2|GE0/0/2"}
        ],
        "device_type": "router",
        "vendor": "huawei",
        "stream": False
    }
    
    print("1. 传统方式（向后兼容）:")
    print("curl -X POST 'http://localhost:8000/generate-config' \\")
    print("  -H 'Content-Type: application/json' \\")
    print(f"  -d '{json.dumps(traditional_request, ensure_ascii=False)}'")
    
    # 2. 新的attachment方式
    new_request = {
        "messages": [
            {
                "role": "user", 
                "content": "请根据这个Excel表格生成BGP配置",
                "attachment": [
                    {"BGP AS号": "65001", "设备名": "Router1", "接口": "GE0/0/1"},
                    {"BGP AS号": "65002", "设备名": "Router2", "接口": "GE0/0/2"}
                ]
            }
        ],
        "device_type": "router",
        "vendor": "huawei", 
        "stream": False
    }
    
    print("\n2. 新的attachment方式:")
    print("curl -X POST 'http://localhost:8000/generate-config' \\")
    print("  -H 'Content-Type: application/json' \\")
    print(f"  -d '{json.dumps(new_request, ensure_ascii=False)}'")
    
    # 3. 文件上传方式
    print("\n3. 文件上传方式:")
    print("curl -X POST 'http://localhost:8000/upload-and-generate' \\")
    print("  -F 'file=@config.xlsx' \\")
    print("  -F 'user_content=请根据这个Excel文件生成BGP配置' \\")
    print("  -F 'device_type=router' \\")
    print("  -F 'vendor=huawei' \\")
    print("  -F 'stream=false'")
    print("  # 注意：实际项目中不需要文件上传API")


def example_benefits():
    """展示新方案的优势"""
    print("\n=== 新方案的优势 ===")
    
    print("1. 数据分离清晰:")
    print("   - content: 用户的文本描述和需求")
    print("   - attachment: 纯净的附件数据（Excel结构化数据或TXT字符串）")
    
    print("\n2. 类型安全:")
    print("   - Excel数据: List[Dict[str, Any]] - 结构化数据")
    print("   - TXT数据: str - 纯文本内容")
    
    print("\n3. 处理精准:")
    print("   - customize场景: 直接使用attachment中的结构化数据，与Jinja2模板结合")
    print("   - open场景: 分别处理示例和需求数据")
    print("   - general场景: 主要使用content，attachment作为补充信息")
    
    print("\n4. 向后兼容:")
    print("   - 现有的content解析逻辑保持不变")
    print("   - 新的attachment字段为可选字段")
    print("   - 优先使用attachment，降级到content")
    
    print("\n5. 错误减少:")
    print("   - 避免了文本解析错误")
    print("   - 数据格式更加标准化")
    print("   - 减少了数据混合导致的问题")


async def main():
    """主函数"""
    await example_direct_api_usage()
    example_customize_scenario()
    example_curl_commands()
    example_benefits()


if __name__ == "__main__":
    asyncio.run(main())
