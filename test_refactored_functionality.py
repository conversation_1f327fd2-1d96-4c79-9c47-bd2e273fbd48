#!/usr/bin/env python3
"""
重构后功能测试脚本
测试配置生成和配置推荐接口是否正常工作
"""

import asyncio
import json
import time
import requests
from typing import Dict, Any

# 测试配置
BASE_URL = "http://localhost:8000"
TIMEOUT = 30


class FunctionalityTester:
    """功能测试器"""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = TIMEOUT
    
    def test_health_check(self) -> bool:
        """测试健康检查接口"""
        try:
            print("🔍 测试健康检查接口...")
            response = self.session.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查成功: {data.get('message', 'N/A')}")
                print(f"   版本: {data.get('version', 'N/A')}")
                print(f"   运行时间: {data.get('uptime', 'N/A'):.2f}秒")
                return True
            else:
                print(f"❌ 健康检查失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 健康检查异常: {str(e)}")
            return False
    
    def test_device_mapping(self) -> bool:
        """测试设备映射功能"""
        try:
            print("\n🔍 测试设备映射功能...")
            
            # 测试用例
            test_cases = [
                {"vendor": "华为", "device": "路由器", "model": "NE40E", "expected": "150"},
                {"vendor": "华为", "device": "交换机", "model": "16800", "expected": "150"},
                {"vendor": "华为", "device": "防火墙", "model": "9000E", "expected": "150"},
                {"vendor": "中兴", "device": "路由器", "model": "M6000", "expected": "150"},
                {"vendor": "中兴", "device": "交换机", "model": "9900X", "expected": "150"},
                {"vendor": "中兴", "device": "交换机", "model": "5960X", "expected": "150"},
                {"vendor": "华三", "device": "交换机", "model": "S6800", "expected": "150"},
                {"vendor": "华三", "device": "交换机", "model": "S12500G-AF", "expected": "150"},
            ]
            
            # 导入设备映射服务
            from app.utils.device_mapping import device_mapping_service
            
            success_count = 0
            for case in test_cases:
                kb_id = device_mapping_service.get_knowledge_base_id(
                    case["vendor"], case["device"], case["model"]
                )
                
                if kb_id == case["expected"]:
                    print(f"✅ {case['vendor']} {case['device']} {case['model']} -> {kb_id}")
                    success_count += 1
                else:
                    print(f"❌ {case['vendor']} {case['device']} {case['model']} -> {kb_id} (期望: {case['expected']})")
            
            print(f"设备映射测试结果: {success_count}/{len(test_cases)} 通过")
            return success_count == len(test_cases)
            
        except Exception as e:
            print(f"❌ 设备映射测试异常: {str(e)}")
            return False
    
    def test_config_generation_api(self) -> bool:
        """测试配置生成API"""
        try:
            print("\n🔍 测试配置生成API...")
            
            # 测试数据
            test_request = {
                "messages": [
                    {
                        "role": "user",
                        "content": "配置华为路由器NE40E的BGP"
                    }
                ],
                "device_type": "router",
                "vendor": "huawei",
                "stream": False
            }
            
            response = self.session.post(
                f"{self.base_url}/generate-config",
                json=test_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 配置生成API调用成功")
                print(f"   响应类型: {type(data)}")
                if isinstance(data, dict) and "success" in data:
                    print(f"   成功状态: {data.get('success', False)}")
                return True
            else:
                print(f"❌ 配置生成API失败: HTTP {response.status_code}")
                print(f"   响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 配置生成API测试异常: {str(e)}")
            return False
    
    def test_config_recommend_api(self) -> bool:
        """测试配置推荐API"""
        try:
            print("\n🔍 测试配置推荐API...")
            
            # 测试数据
            test_request = {
                "messages": [
                    {
                        "role": "user",
                        "content": "我需要配置华为交换机16800的VLAN"
                    }
                ],
                "stream": False
            }
            
            response = self.session.post(
                f"{self.base_url}/config-recommend",
                json=test_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 配置推荐API调用成功")
                print(f"   响应类型: {type(data)}")
                if isinstance(data, dict):
                    print(f"   响应键: {list(data.keys())}")
                return True
            else:
                print(f"❌ 配置推荐API失败: HTTP {response.status_code}")
                print(f"   响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 配置推荐API测试异常: {str(e)}")
            return False
    
    def test_supported_devices_api(self) -> bool:
        """测试支持设备列表API"""
        try:
            print("\n🔍 测试支持设备列表API...")
            
            response = self.session.get(f"{self.base_url}/config-recommend/devices")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 支持设备列表API调用成功")
                
                if isinstance(data, dict) and "data" in data:
                    devices = data["data"]
                    print(f"   支持的厂商数量: {len(devices)}")
                    for vendor, device_types in devices.items():
                        print(f"   {vendor}: {list(device_types.keys())}")
                
                return True
            else:
                print(f"❌ 支持设备列表API失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 支持设备列表API测试异常: {str(e)}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        print("🚀 开始功能测试...")
        print("=" * 60)
        
        results = {}
        
        # 运行各项测试
        results["health_check"] = self.test_health_check()
        results["device_mapping"] = self.test_device_mapping()
        results["config_generation_api"] = self.test_config_generation_api()
        results["config_recommend_api"] = self.test_config_recommend_api()
        results["supported_devices_api"] = self.test_supported_devices_api()
        
        # 汇总结果
        print("\n" + "=" * 60)
        print("📊 测试结果汇总:")
        
        passed = 0
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总体结果: {passed}/{total} 测试通过")
        
        if passed == total:
            print("🎉 所有测试都通过了！重构成功！")
        else:
            print("⚠️  部分测试失败，请检查相关功能")
        
        return results


def main():
    """主函数"""
    print("重构后功能测试脚本")
    print("请确保服务已启动在 http://localhost:8000")
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    # 创建测试器并运行测试
    tester = FunctionalityTester()
    results = tester.run_all_tests()
    
    # 返回退出码
    all_passed = all(results.values())
    exit(0 if all_passed else 1)


if __name__ == "__main__":
    main()
