from fastapi import FastAPI, Request
import uvicorn

app = FastAPI()

@app.get("/")
async def read_root():
    return {"Hello": "World"}

@app.post("/vector_search")
async def handle_request(request: Request):
    """
    处理特定请求的接口。
    预期接收的 URL、请求头和 JSON 请求体。
    """
    # 这里你可以选择性地打印或验证请求的详细信息
    print(f"Received request on path: {request.url.path}")
    print(f"Request headers: {request.headers}")

    try:
        request_body = await request.json()
        print(f"Request body: {request_body}")
        
        # 验证请求体是否符合预期
        expected_payload = {"appId":"urdDUFiZhKrZi","topK":5,"categoryId":153,"query":"snmp","similarity":0.4}
        if request_body == expected_payload:
            print("Request body matches the expected payload.")
        else:
            print("Warning: Request body does not exactly match the expected payload.")

    except Exception as e:
        print(f"Could not parse request body as JSON: {e}")
        request_body = None # 或者根据需要处理错误

    # 返回固定响应
    return {
    "code": 200,
    "data": {
        "metricType": 2,
        "query": "snmp",
        "segments": [
            {
                "answer": '',
                "categoryName": "配置生成POC",
                "content": "#\r\n//这是SNMP系统信息和安全配置，启用所有版本的SNMP协议，创建SNMPv3安全组dc-admin，配置隐私安全级别，设置读视图rd、写视图wt和通知视图nt，配置两个SNMP告警目标主机************和************，使用加密的安全名称参数。这些配置增强了SNMP的安全性，并确保告警信息能够发送到指定的网管服务器。//\r\nsnmp-agent sys-info version all\r\nsnmp-agent group v3 dc-admin privacy read-view rd write-view wt notify-view nt\r\nsnmp-agent target-host trap address udp-domain ************ params securityname cipher %^%#w/5&T2u\"t5DI3>PgpX^E.ZS7Xv06&*V1(.WuY|YM%^%#\r\nsnmp-agent target-host trap address udp-domain ************ params securityname cipher %^%#ACw70[\"Y|C$>TkG=Yp;(q1\\3+Xv`Y84qVWU[0~2K%^%#\r\n",
                "fileName": "section_059.txt",
                "id": 261397,
                "question": '',
                "similarity": 0.59224916,
                "type": 2,
                "vectorId": "2_153_261397"
            },
            {
                "answer": '',
                "categoryName": "配置生成POC",
                "content": "#\r\n//这是SNMP MIB视图和用户配置，创建四个MIB视图nt、rd、wt和iso-view，均包含整个ISO MIB树，创建SNMPv3用户admin和uhmroot，将uhmroot用户加入dc-admin组，配置uhmroot用户使用SHA认证模式和AES128加密模式，并设置加密的认证密码和隐私密码。这些配置定义了SNMP用户的访问权限和安全参数，确保只有授权用户能够安全地访问和管理设备。//\r\nsnmp-agent mib-view included nt iso\r\nsnmp-agent mib-view included rd iso\r\nsnmp-agent mib-view included wt iso\r\nsnmp-agent mib-view included iso-view iso\r\nsnmp-agent usm-user v3 admin\r\nsnmp-agent usm-user v3 uhmroot\r\nsnmp-agent usm-user v3 uhmroot group dc-admin\r\nsnmp-agent usm-user v3 uhmroot authentication-mode sha cipher %^%#[t9u%9i*fIb6,bSnc:.)4CND9&^ct9>:#ZYuN9C9%^%#\r\nsnmp-agent usm-user v3 uhmroot privacy-mode aes128 cipher %^%#|aY(B5g)}L#AyBWW)KnBVM5F.8r%o~Q{TWW/sEEC%^%#\r\n",
                "fileName": "section_060.txt",
                "id": 261399,
                "question": '',
                "similarity": 0.5656831,
                "type": 2,
                "vectorId": "2_153_261399"
            },
            {
                "answer": '',
                "categoryName": "配置生成POC",
                "content": "#\r\n//这是SNMP配置命令，指定GigabitEthernet0/0/0接口作为SNMP trap消息的源接口，用于发送告警信息时固定源地址。//\r\nsnmp-agent trap source GigabitEthernet0/0/0\r\n",
                "fileName": "section_141.txt",
                "id": 261461,
                "question": '',
                "similarity": 0.5648669,
                "type": 2,
                "vectorId": "2_153_261461"
            },
            {
                "answer": '',
                "categoryName": "配置生成POC",
                "content": "#\r\n//这是SNMP告警使能配置，全局启用SNMP告警功能。这允许设备在发生重要事件（如接口状态变化、温度超限等）时，主动向网管服务器发送告警信息，便于网络管理员及时发现和处理网络问题。//\r\nsnmp-agent trap enable\r\n",
                "fileName": "section_062.txt",
                "id": 261403,
                "question": '',
                "similarity": 0.5559512,
                "type": 2,
                "vectorId": "2_153_261403"
            },
            {
                "answer": '',
                "categoryName": "配置生成POC",
                "content": "#\r\n//这是SNMP配置命令，启用SNMP trap功能，允许设备主动发送告警信息给网管系统。//\r\nsnmp-agent trap enable\r\n",
                "fileName": "section_144.txt",
                "id": 261455,
                "question": '',
                "similarity": 0.5556872,
                "type": 2,
                "vectorId": "2_153_261455"
            }
        ]
    },
    "msg": "success"
}

if __name__ == "__main__":
    # 运行 FastAPI 应用在 8888 端口
    uvicorn.run(app, host="0.0.0.0", port=8888)