"""
请求模型定义
"""

from typing import Optional, List, Any, Dict, Union
from pydantic import BaseModel, Field, field_validator
from app.config.constants import IntentType, DeviceType, VendorType


class Message(BaseModel):
    """LLM消息模型"""
    role: str = Field(..., description="消息角色：user, assistant, system")
    content: str = Field(..., description="消息内容")
    vendor: Optional[str] = Field(None, description="设备厂商")
    device: Optional[str] = Field(None, description="设备类型")
    model: Optional[str] = Field(None, description="设备型号")
    attachments: Optional[List[Dict[str, Any]]] = Field(None, description="附件列表")
    attachment: Optional[Union[List[Dict[str, Any]], str]] = Field(
        default=None,
        description="附件内容：Excel解析后为List[Dict]，TXT文件为str"
    )


class ConfigGenerationRequest(BaseModel):
    """配置生成请求模型"""

    messages: List[Message] = Field(..., description="对话消息列表，支持多轮对话")
    device_type: Optional[str] = Field(None, description="设备类型")
    vendor: Optional[str] = Field(None, description="厂商名称")
    stream: bool = Field(default=False, description="是否流式返回")

    class Config:
        json_schema_extra = {
            "example": {
                "messages": [
                    {"role": "user", "content": "请生成一个BGP配置"}
                ],
                "device_type": "router",
                "vendor": "huawei",
                "stream": True
            }
        }


class ConfigRecommendRequest(BaseModel):
    """配置推荐请求模型"""

    messages: List[Message] = Field(..., description="对话消息列表，支持多轮对话")
    stream: bool = Field(default=False, description="是否流式返回")
    isDirectMilvus: Optional[bool] = Field(False, description="是否直连milvus知识检索")
    milvusSearchParams: Optional[Dict[str, Any]] = Field(None, description="milvus的查询优化参数")

    @field_validator('messages')
    @classmethod
    def validate_messages_not_empty(cls, v):
        if not v:
            raise ValueError('消息列表不能为空')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "messages": [
                    {
                        "role": "user",
                        "content": "如何配置华为路由器的BGP协议？",
                        "vendor": "huawei",
                        "device": "router",
                        "model": "NE40E",
                        "attachments": []
                    }
                ],
                "stream": False,
                "isDirectMilvus": False,
                "milvusSearchParams": {
                    "top_k": 5,
                    "similarity_threshold": 0.7
                }
            }
        }


class CustomizedCapabilityRequest(BaseModel):
    """定制化能力请求模型"""

    messages: List[Message] = Field(..., description="对话消息列表")
    device_type: str = Field(..., description="设备类型")
    vendor: str = Field(..., description="厂商名称")

    class Config:
        json_schema_extra = {
            "example": {
                "messages": [
                    {"role": "user", "content": "CE端BGP AS号|CE端设备|设备型号..."}
                ],
                "device_type": "router",
                "vendor": "huawei"
            }
        }





class OpenCapabilityRequest(BaseModel):
    """开放式能力请求模型"""

    messages: List[Message] = Field(..., description="对话消息列表")
    device_type: str = Field(..., description="设备类型")
    vendor: str = Field(..., description="厂商名称")

    class Config:
        json_schema_extra = {
            "example": {
                "messages": [
                    {"role": "user", "content": "示例Excel: ...\n示例TXT: ...\n需求Excel: ..."}
                ],
                "device_type": "router",
                "vendor": "huawei"
            }
        }





class GeneralCapabilityRequest(BaseModel):
    """通用能力请求模型"""

    messages: List[Message] = Field(..., description="对话消息列表")
    device_type: str = Field(..., description="设备类型")
    vendor: str = Field(..., description="厂商名称")

    class Config:
        json_schema_extra = {
            "example": {
                "messages": [
                    {"role": "user", "content": "如何配置BGP邻居"}
                ],
                "device_type": "router",
                "vendor": "huawei"
            }
        }

class AnotherCapabilityRequest(BaseModel):
    """通用能力请求模型"""

    messages: List[Message] = Field(..., description="对话消息列表")
    device_type: str = Field(..., description="设备类型")
    vendor: str = Field(..., description="厂商名称")

    class Config:
        json_schema_extra = {
            "example": {
                "messages": [
                    {"role": "user", "content": "因集团IPTV业务需求，需新联通云中原O域资源池通过承载B MVSP_NMS VPN与各省对接，申请资源池承载B网MVSP_NMS接收地址需求如下： 原接收地址范围由**********/28调整为*********/27（河南）；新增接收地址范围*********/24（黑龙江）；"}
                ],
                "device_type": "router",
                "vendor": "huawei"
            }
        }




class IntentRecognitionRequest(BaseModel):
    """意图识别请求模型"""
    
    user_input: str = Field(..., description="用户输入文本")
    
    class Config:
        json_schema_extra = {
            "example": {
                "user_input": "请根据这个表格生成配置..."
            }
        }


class FileUploadRequest(BaseModel):
    """文件上传请求模型"""
    
    file_content: bytes = Field(..., description="文件内容")
    file_name: str = Field(..., description="文件名")
    file_type: str = Field(..., description="文件类型")
    
    class Config:
        json_schema_extra = {
            "example": {
                "file_name": "config.xlsx",
                "file_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            }
        }
