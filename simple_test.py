#!/usr/bin/env python3
"""
简化的功能测试脚本
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        print(f"健康检查: {response.status_code} - {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_device_mapping():
    """测试设备映射"""
    try:
        from app.utils.device_mapping import device_mapping_service
        kb_id = device_mapping_service.get_knowledge_base_id("华为", "路由器", "NE40E")
        print(f"设备映射测试: 华为路由器NE40E -> {kb_id}")
        return kb_id == "150"
    except Exception as e:
        print(f"设备映射测试失败: {e}")
        return False

def test_config_generation():
    """测试配置生成API"""
    try:
        data = {
            "messages": [{"role": "user", "content": "配置BGP"}],
            "device_type": "router",
            "vendor": "huawei",
            "stream": False
        }
        response = requests.post(f"{BASE_URL}/generate-config", json=data, timeout=30)
        print(f"配置生成API: {response.status_code}")
        if response.status_code != 200:
            print(f"错误详情: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"配置生成API测试失败: {e}")
        return False

def test_config_recommend():
    """测试配置推荐API"""
    try:
        data = {
            "messages": [{"role": "user", "content": "配置VLAN"}],
            "stream": False
        }
        response = requests.post(f"{BASE_URL}/config-recommend", json=data, timeout=30)
        print(f"配置推荐API: {response.status_code}")
        if response.status_code != 200:
            print(f"错误详情: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"配置推荐API测试失败: {e}")
        return False

def test_devices_api():
    """测试设备列表API"""
    try:
        response = requests.get(f"{BASE_URL}/config-recommend/devices", timeout=10)
        print(f"设备列表API: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"支持的厂商: {list(data.get('data', {}).keys())}")
        return response.status_code == 200
    except Exception as e:
        print(f"设备列表API测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始简化测试...")
    
    tests = [
        ("健康检查", test_health),
        ("设备映射", test_device_mapping),
        ("配置生成API", test_config_generation),
        ("配置推荐API", test_config_recommend),
        ("设备列表API", test_devices_api),
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n🔍 测试 {name}...")
        result = test_func()
        results.append((name, result))
        print(f"{'✅' if result else '❌'} {name}: {'通过' if result else '失败'}")
    
    print(f"\n📊 测试汇总:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        print(f"   {name}: {'✅ 通过' if result else '❌ 失败'}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！重构成功！")
    else:
        print("⚠️  部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
