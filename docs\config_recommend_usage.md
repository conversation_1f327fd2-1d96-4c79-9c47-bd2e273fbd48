# 配置推荐功能使用指南

## 概述

配置推荐功能是一个基于RAG（检索增强生成）的智能配置推荐系统，能够根据用户的问题和设备信息，从知识库中检索相关文档，并使用LLM生成专业的配置推荐步骤。

## 功能特性

- **多厂商支持**：支持华为、思科、华三、瞻博等主流网络设备厂商
- **智能设备识别**：自动从用户问题中提取厂商、设备类型、型号信息
- **知识库映射**：根据设备信息智能选择对应的知识库
- **多轮对话**：支持多轮对话，保持上下文连续性
- **流式响应**：支持流式和非流式两种响应模式
- **意图识别**：智能识别用户意图，过滤非配置相关问题

## API接口

### 基础配置推荐接口

**端点**: `POST /api/v1/config-recommend`

**请求参数**:

```json
{
  "messages": [
    {
      "role": "user",
      "content": "华为NE40E路由器如何配置BGP协议？"
    }
  ],
  "stream": false,
  "厂商": "华为",
  "设备": "路由器", 
  "型号": "NE40E",
  "附件": [],
  "isDirectMilvus": false,
  "milvusSearchParams": {}
}
```

**响应示例**:

```json
{
  "success": true,
  "message": "配置推荐生成成功",
  "code": 200,
  "config_content": "根据您的需求，为华为NE40E路由器配置BGP协议的步骤如下：\n\n1. 进入系统视图\n[Huawei] system-view\n\n2. 进入BGP视图\n[Huawei] bgp 65001\n\n3. 配置BGP邻居\n[Huawei-bgp] peer 192.168.1.1 as-number 65002\n\n...",
  "intent_type": "config_recommend",
  "device_info": {
    "厂商": "华为",
    "设备": "路由器",
    "型号": "NE40E"
  },
  "knowledge_base_id": "kb_huawei_router_ne40e",
  "extracted_info": {
    "厂商": "华为",
    "设备": "路由器",
    "型号": "NE40E",
    "原始问题": "华为NE40E路由器如何配置BGP协议？",
    "改写问题": "华为NE40E路由器BGP协议配置方法"
  }
}
```

### 流式响应

设置 `"stream": true` 可以获得流式响应：

```bash
curl -X POST "http://localhost:8000/api/v1/config-recommend" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "思科路由器如何配置OSPF？"}],
    "stream": true
  }'
```

流式响应格式遵循OpenAI标准：

```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"config-recommend","choices":[{"index":0,"delta":{"content":"正在分析您的配置需求..."},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"config-recommend","choices":[{"index":0,"delta":{"content":"\n\n识别的设备信息：\n厂商: 思科\n设备: 路由器\n型号: 未知"},"finish_reason":null}]}

data: [DONE]
```

## 参数说明

### 必需参数

- **messages**: 对话消息列表，支持多轮对话
  - `role`: 消息角色（"user" 或 "assistant"）
  - `content`: 消息内容

### 可选参数

- **stream**: 是否流式返回（默认: false）
- **厂商**: 设备厂商（华为/思科/华三/瞻博等）
- **设备**: 设备类型（路由器/交换机/防火墙等）
- **型号**: 设备型号（NE40E/Catalyst/MSR等）
- **附件**: 附件列表（暂未使用）
- **isDirectMilvus**: 是否直连Milvus进行知识检索（默认: false）
- **milvusSearchParams**: Milvus搜索优化参数

## 使用示例

### 1. 基础配置推荐

```python
import requests

url = "http://localhost:8000/api/v1/config-recommend"
data = {
    "messages": [
        {"role": "user", "content": "华为交换机如何配置VLAN？"}
    ],
    "厂商": "华为",
    "设备": "交换机"
}

response = requests.post(url, json=data)
result = response.json()
print(result["config_content"])
```

### 2. 多轮对话

```python
# 第一轮对话
data1 = {
    "messages": [
        {"role": "user", "content": "华为路由器如何配置BGP？"}
    ]
}
response1 = requests.post(url, json=data1)

# 第二轮对话（基于第一轮的回复）
data2 = {
    "messages": [
        {"role": "user", "content": "华为路由器如何配置BGP？"},
        {"role": "assistant", "content": response1.json()["config_content"]},
        {"role": "user", "content": "如何配置BGP邻居认证？"}
    ]
}
response2 = requests.post(url, json=data2)
```

### 3. 流式响应处理

```python
import requests

url = "http://localhost:8000/api/v1/config-recommend"
data = {
    "messages": [
        {"role": "user", "content": "思科防火墙如何配置访问控制列表？"}
    ],
    "stream": True
}

response = requests.post(url, json=data, stream=True)

for line in response.iter_lines():
    if line:
        line = line.decode('utf-8')
        if line.startswith('data: '):
            chunk_data = line[6:]  # 去掉 'data: ' 前缀
            if chunk_data != '[DONE]':
                import json
                chunk = json.loads(chunk_data)
                content = chunk["choices"][0]["delta"].get("content", "")
                print(content, end="")
```

## 管理接口

### 获取支持的设备列表

```bash
GET /api/v1/config-recommend/supported-devices
```

### 获取对话列表

```bash
GET /api/v1/config-recommend/conversations
```

### 获取特定对话信息

```bash
GET /api/v1/config-recommend/conversations/{conversation_id}
```

### 删除对话

```bash
DELETE /api/v1/config-recommend/conversations/{conversation_id}
```

### 清空所有对话

```bash
DELETE /api/v1/config-recommend/conversations
```

### 设置对话保存轮数

```bash
POST /api/v1/config-recommend/settings/conversation-rounds
Content-Type: application/json

3
```

### 健康检查

```bash
GET /api/v1/config-recommend/health
```

## 错误处理

### 常见错误码

- **400**: 请求参数错误
- **404**: 对话不存在
- **500**: 服务器内部错误

### 错误响应示例

```json
{
  "success": false,
  "message": "我只回答关于云池数通设备配置推荐的内容",
  "code": 400,
  "intent_type": "invalid"
}
```

## 最佳实践

1. **明确设备信息**: 在请求中尽量提供完整的厂商、设备类型、型号信息
2. **使用多轮对话**: 对于复杂配置需求，可以通过多轮对话逐步细化
3. **流式响应**: 对于复杂配置推荐，建议使用流式响应提升用户体验
4. **错误处理**: 始终检查响应中的success字段和错误信息
5. **对话管理**: 定期清理不需要的对话历史，避免内存占用过多

## 注意事项

- 系统默认保存最近3轮对话历史
- 对话会在2小时无活动后自动过期
- 非配置相关的问题会被拒绝回答
- 建议在生产环境中配置适当的速率限制
