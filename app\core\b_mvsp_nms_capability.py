"""
承载B网MVSP_NMS配置生成能力模块

基于LLM分析用户需求，生成承载B网MVSP_NMS相关配置
"""

from typing import Dict, Any, AsyncGenerator
from loguru import logger

from app.core.base_capability import LLMBasedCapability
from app.models.request import ConfigGenerationRequest


class BMVSPNMSCapability(LLMBasedCapability):
    """承载B网MVSP_NMS配置生成能力类"""

    def __init__(self):
        super().__init__("b-mvsp-nms-capability")

        # LLM提示词模板
        self.prompt_template = """你是一个专业的网络设备配置专家，专门处理承载B网MVSP_NMS的IP地址列表管理配置。

用户需求：{user_input}

设备类型：{device_type}
厂商：{vendor}

请根据用户需求分析IP地址操作，并生成相应的配置命令。

配置要求：
1. 华为设备使用 ip ip-prefix pl-MVSP_NMS_B-v4-in 格式
3. 如果涉及地址范围调整，需要生成新增配置和删除原有配置的命令
4. 如果是新增地址，直接生成新增配置
5. 配置中要包含适当的注释说明

示例输出格式（华为）：
```
#原有的配置
ip ip-prefix pl-MVSP_NMS_B-v4-in index 10 permit ************ 28

#新增的配置
ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 27
ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 24
#回收原有地址列表
undo ip ip-prefix pl-MVSP_NMS_B-v4-in index 10
#
```

请直接输出配置命令，不要其他解释。"""

    def _prepare_generation_context(self, request: ConfigGenerationRequest) -> Dict[str, Any]:
        """准备配置生成的上下文"""
        # 从消息中提取用户输入
        user_input = self._extract_user_input_from_messages(request.messages)

        # 构建系统提示词
        system_prompt = self.prompt_template.format(
            user_input=user_input,
            device_type=getattr(request, 'device_type', 'router'),
            vendor=getattr(request, 'vendor', 'huawei')
        )

        # 构建完整的消息列表，包含系统提示和用户对话历史
        messages = [{"role": "system", "content": system_prompt}]

        # 添加用户的对话历史
        for msg in request.messages:
            messages.append({"role": msg.role, "content": msg.content})

        return {
            "user_input": user_input,
            "device_type": getattr(request, 'device_type', 'router'),
            "vendor": getattr(request, 'vendor', 'huawei'),
            "messages": messages
        }

    async def _generate_config_with_llm(self, user_input: str, device_type: str, vendor: str) -> str:
        """使用LLM生成配置（兼容旧接口）"""
        try:
            # 构建提示词
            prompt = self.prompt_template.format(
                user_input=user_input,
                device_type=device_type,
                vendor=vendor
            )

            # 调用LLM
            messages = [{"role": "user", "content": prompt}]
            response = await self._call_llm(messages)

            # 提取配置内容
            config_content = self._extract_content_from_response(response)

            return config_content

        except Exception as e:
            logger.error(f"LLM配置生成失败: {str(e)}")
            return f"配置生成失败: {str(e)}"

    async def generate_config(self, request: ConfigGenerationRequest) -> str:
        """生成配置（非流式返回）"""
        try:
            # 准备生成上下文（支持多轮对话）
            context = self._prepare_generation_context(request)

            logger.info(f"B_MVSP_NMS配置生成: 设备类型={context['device_type']}, 厂商={context['vendor']}")

            # 使用完整的消息历史调用LLM
            response = await self._call_llm(context['messages'])

            # 提取配置内容
            config_content = self._extract_content_from_response(response)

            return config_content

        except Exception as e:
            logger.error(f"B_MVSP_NMS配置生成失败: {str(e)}")
            return f"配置生成失败: {str(e)}"

    async def generate_config_stream(self, request: ConfigGenerationRequest) -> AsyncGenerator[Dict[str, Any], None]:
        """生成配置（流式返回）"""
        try:
            # 准备生成上下文（支持多轮对话）
            context = self._prepare_generation_context(request)

            logger.info(f"B_MVSP_NMS流式配置生成: 设备类型={context['device_type']}, 厂商={context['vendor']}")

            # 使用完整的消息历史调用LLM流式接口
            async for chunk in self._call_llm_stream(context['messages']):
                yield chunk

        except Exception as e:
            logger.error(f"B_MVSP_NMS流式配置生成失败: {str(e)}")
            yield self._create_error_chunk(f"配置生成失败: {str(e)}")

    def _extract_user_input_from_messages(self, messages: list) -> str:
        """从消息列表中提取用户输入内容"""
        # 从后往前查找最新的用户消息
        for message in reversed(messages):
            if message.role == "user":
                # 优先从attachment字段获取附件内容
                if hasattr(message, 'attachment') and message.attachment is not None:
                    if isinstance(message.attachment, str):
                        return message.attachment.strip()
                
                # 从content获取
                return message.content.strip()
        return ""


# 全局B_MVSP_NMS能力实例
b_mvsp_nms_capability = BMVSPNMSCapability()
