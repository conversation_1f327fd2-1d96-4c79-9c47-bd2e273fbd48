"""
客户端断开连接监控工具
提供通用的客户端断开连接检测和处理功能
"""

import asyncio
from typing import Optional
from loguru import logger
from fastapi import Request


class ClientDisconnectMonitor:
    """客户端断开连接监控器"""
    
    def __init__(
        self,
        http_request: Request,
        cancellation_token: asyncio.Event,
        is_streaming: bool = False,
        stream_check_interval: float = 0.5,
        stream_disconnect_threshold: int = 3,
        non_stream_check_interval: float = 10.0,  # 增加非流式检测间隔
        non_stream_disconnect_threshold: int = 50,  # 大幅增加非流式阈值
        enable_monitoring: bool = None  # 新增：是否启用监控
    ):
        """
        初始化断开连接监控器

        Args:
            http_request: FastAPI请求对象
            cancellation_token: 取消令牌
            is_streaming: 是否为流式请求
            stream_check_interval: 流式请求检测间隔（秒）
            stream_disconnect_threshold: 流式请求断开确认次数
            non_stream_check_interval: 非流式请求检测间隔（秒）
            non_stream_disconnect_threshold: 非流式请求断开确认次数
            enable_monitoring: 是否启用监控（None=自动决定，True=强制启用，False=禁用）
        """
        self.http_request = http_request
        self.cancellation_token = cancellation_token
        self.is_streaming = is_streaming

        # 根据请求类型设置检测参数
        if is_streaming:
            self.check_interval = stream_check_interval
            self.disconnect_threshold = stream_disconnect_threshold
        else:
            self.check_interval = non_stream_check_interval
            self.disconnect_threshold = non_stream_disconnect_threshold

        # 决定是否启用监控
        if enable_monitoring is None:
            # 自动决定：流式请求启用，非流式请求暂时禁用（避免误判）
            self.monitoring_enabled = is_streaming
        else:
            self.monitoring_enabled = enable_monitoring

        self.monitor_task: Optional[asyncio.Task] = None
        self._disconnect_count = 0
    
    async def _monitor_client_disconnect(self):
        """监控客户端连接状态的内部方法"""
        try:
            # 如果监控被禁用，直接返回
            if not self.monitoring_enabled:
                logger.debug(f"客户端断开连接监控已禁用（{'流式' if self.is_streaming else '非流式'}）")
                return

            consecutive_errors = 0
            max_consecutive_errors = 5

            while not self.cancellation_token.is_set():
                try:
                    # 检查客户端连接状态
                    is_disconnected = False

                    try:
                        is_disconnected = self.http_request.is_disconnected()
                        consecutive_errors = 0  # 重置错误计数
                    except Exception as check_error:
                        consecutive_errors += 1
                        logger.warning(f"检查连接状态时出错 ({consecutive_errors}/{max_consecutive_errors}): {str(check_error)}")

                        # 如果连续出错太多次，假设连接正常（保守策略）
                        if consecutive_errors >= max_consecutive_errors:
                            logger.warning("连续检查连接状态出错过多，假设连接正常")
                            is_disconnected = False
                            consecutive_errors = 0
                        else:
                            # 出错时跳过本次检查
                            await asyncio.sleep(self.check_interval)
                            continue

                    # 处理断开检测结果
                    if is_disconnected:
                        self._disconnect_count += 1
                        logger.debug(
                            f"检测到客户端断开 {self._disconnect_count}/{self.disconnect_threshold} "
                            f"({'流式' if self.is_streaming else '非流式'})"
                        )

                        if self._disconnect_count >= self.disconnect_threshold:
                            logger.info(
                                f"确认客户端断开连接（{'流式' if self.is_streaming else '非流式'}），设置取消令牌"
                            )
                            self.cancellation_token.set()
                            break
                    else:
                        if self._disconnect_count > 0:
                            logger.debug("客户端连接恢复，重置断开计数器")
                        self._disconnect_count = 0  # 重置计数器

                    await asyncio.sleep(self.check_interval)

                except asyncio.CancelledError:
                    # 正常取消，退出循环
                    break
                except Exception as e:
                    logger.error(f"监控循环中出现未预期错误: {str(e)}")
                    await asyncio.sleep(self.check_interval)

        except Exception as e:
            logger.error(f"监控客户端连接状态时出错: {str(e)}")
    
    def start_monitoring(self) -> asyncio.Task:
        """
        启动客户端断开连接监控
        
        Returns:
            监控任务对象
        """
        if self.monitor_task is not None:
            logger.warning("监控任务已经在运行中")
            return self.monitor_task
        
        self.monitor_task = asyncio.create_task(self._monitor_client_disconnect())

        if self.monitoring_enabled:
            logger.debug(
                f"启动客户端断开连接监控 ({'流式' if self.is_streaming else '非流式'}), "
                f"检测间隔: {self.check_interval}s, 断开阈值: {self.disconnect_threshold}"
            )
        else:
            logger.debug(
                f"客户端断开连接监控已禁用 ({'流式' if self.is_streaming else '非流式'})"
            )
        return self.monitor_task
    
    async def stop_monitoring(self):
        """停止客户端断开连接监控"""
        if self.monitor_task is not None:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
            finally:
                self.monitor_task = None
                logger.debug("客户端断开连接监控已停止")
    
    @property
    def is_cancelled(self) -> bool:
        """检查是否已被取消"""
        return self.cancellation_token.is_set()
    
    @property
    def disconnect_count(self) -> int:
        """获取当前断开检测次数"""
        return self._disconnect_count


def create_disconnect_monitor(
    http_request: Request,
    cancellation_token: asyncio.Event,
    is_streaming: bool = False
) -> ClientDisconnectMonitor:
    """
    创建客户端断开连接监控器的便捷函数
    
    Args:
        http_request: FastAPI请求对象
        cancellation_token: 取消令牌
        is_streaming: 是否为流式请求
    
    Returns:
        ClientDisconnectMonitor实例
    """
    return ClientDisconnectMonitor(
        http_request=http_request,
        cancellation_token=cancellation_token,
        is_streaming=is_streaming
    )


class DisconnectMonitorContext:
    """断开连接监控上下文管理器"""

    def __init__(self, http_request: Request, is_streaming: bool = False):
        self.http_request = http_request
        self.is_streaming = is_streaming
        self.cancellation_token = None
        self.monitor = None

    async def __aenter__(self):
        """进入上下文"""
        self.cancellation_token = asyncio.Event()
        self.monitor = create_disconnect_monitor(
            http_request=self.http_request,
            cancellation_token=self.cancellation_token,
            is_streaming=self.is_streaming
        )

        # 启动监控
        self.monitor.start_monitoring()
        return self.cancellation_token, self.monitor

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        if self.monitor:
            await self.monitor.stop_monitoring()


def with_disconnect_monitoring(
    http_request: Request,
    is_streaming: bool = False
) -> DisconnectMonitorContext:
    """
    创建断开连接监控上下文管理器

    Args:
        http_request: FastAPI请求对象
        is_streaming: 是否为流式请求

    Returns:
        DisconnectMonitorContext: 上下文管理器实例
    """
    return DisconnectMonitorContext(http_request, is_streaming)


# 向后兼容的函数名
create_client_disconnect_monitor = create_disconnect_monitor
