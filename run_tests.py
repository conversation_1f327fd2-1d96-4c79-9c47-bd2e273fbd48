#!/usr/bin/env python3
"""
B_MVSP_NMS核心API测试运行脚本
支持分模块测试和完整测试
"""

import sys
import asyncio
import argparse
from test.test_b_mvsp_nms_core import TestBMVSPNMSCore


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='B_MVSP_NMS核心API测试')
    parser.add_argument(
        'test_type',
        choices=['intent', 'capability', 'multi-turn', 'pipeline', 'all', 'pytest'],
        help='测试类型: intent(意图识别), capability(能力测试), multi-turn(多轮对话), pipeline(完整流程), all(所有测试), pytest(使用pytest运行)'
    )
    
    args = parser.parse_args()
    
    if args.test_type == 'pytest':
        # 使用pytest运行
        import subprocess
        result = subprocess.run(['uv', 'run', 'python', '-m', 'pytest', 'test/test_b_mvsp_nms_core.py', '-v'], 
                              capture_output=False)
        sys.exit(result.returncode)
    
    # 创建测试实例
    test_instance = TestBMVSPNMSCore()
    test_instance.setup_method()
    
    print(f"🚀 开始运行 {args.test_type} 测试...")
    
    try:
        if args.test_type == 'intent':
            test_instance.run_intent_tests()
        elif args.test_type == 'capability':
            asyncio.run(test_instance.run_capability_tests())
        elif args.test_type == 'multi-turn':
            test_instance.run_multi_turn_tests()
            asyncio.run(test_instance.test_multi_turn_config_generation())
        elif args.test_type == 'pipeline':
            asyncio.run(test_instance.run_pipeline_tests())
        elif args.test_type == 'all':
            asyncio.run(test_instance.run_all_tests())
        
        print("🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
