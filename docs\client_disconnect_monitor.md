# 客户端断开连接监控工具

## 概述

`ClientDisconnectMonitor` 是一个通用的客户端断开连接监控工具，用于检测HTTP客户端是否断开连接，并在检测到断开时设置取消令牌，以便优雅地取消正在进行的操作。

## 特性

- **流式和非流式请求支持**：针对不同类型的请求使用不同的检测参数
- **可配置的检测参数**：支持自定义检测间隔和断开阈值
- **断开计数器重置**：当连接恢复时自动重置计数器，避免误判
- **上下文管理器支持**：提供便捷的上下文管理器接口
- **异步任务管理**：自动管理监控任务的生命周期

## 基本用法

### 1. 基本使用

```python
from app.utils.client_disconnect_monitor import create_disconnect_monitor
import asyncio

async def my_endpoint(request: ConfigRequest, http_request: Request):
    # 创建取消令牌
    cancellation_token = asyncio.Event()
    
    # 创建监控器
    disconnect_monitor = create_disconnect_monitor(
        http_request=http_request,
        cancellation_token=cancellation_token,
        is_streaming=request.stream
    )
    
    try:
        # 启动监控
        disconnect_monitor.start_monitoring()
        
        # 执行业务逻辑
        if request.stream:
            return StreamingResponse(my_stream_generator(cancellation_token))
        else:
            return await my_non_stream_handler(cancellation_token)
            
    finally:
        # 清理监控任务
        await disconnect_monitor.stop_monitoring()
```

### 2. 使用上下文管理器

```python
from app.utils.client_disconnect_monitor import with_disconnect_monitoring

async def my_endpoint(request: ConfigRequest, http_request: Request):
    async with with_disconnect_monitoring(http_request, request.stream) as (token, monitor):
        # 执行业务逻辑，token会在客户端断开时被设置
        if request.stream:
            return StreamingResponse(my_stream_generator(token))
        else:
            return await my_non_stream_handler(token)
    # 监控会自动清理
```

## 配置参数

### 默认参数

| 参数类型 | 检测间隔 | 断开阈值 | 说明 |
|---------|---------|---------|------|
| 流式请求 | 0.5秒 | 3次 | 需要更频繁的检测 |
| 非流式请求 | 5.0秒 | 20次 | 检测频率较低，避免误判 |

### 自定义参数

```python
monitor = ClientDisconnectMonitor(
    http_request=http_request,
    cancellation_token=cancellation_token,
    is_streaming=True,
    stream_check_interval=0.2,      # 自定义流式检测间隔
    stream_disconnect_threshold=5,   # 自定义流式断开阈值
    non_stream_check_interval=10.0,  # 自定义非流式检测间隔
    non_stream_disconnect_threshold=30  # 自定义非流式断开阈值
)
```

## API 参考

### ClientDisconnectMonitor

主要的监控器类。

#### 构造函数

```python
def __init__(
    self,
    http_request: Request,
    cancellation_token: asyncio.Event,
    is_streaming: bool = False,
    stream_check_interval: float = 0.5,
    stream_disconnect_threshold: int = 3,
    non_stream_check_interval: float = 5.0,
    non_stream_disconnect_threshold: int = 20
)
```

#### 方法

- `start_monitoring() -> asyncio.Task`: 启动监控任务
- `async stop_monitoring()`: 停止监控任务
- `is_cancelled -> bool`: 检查是否已被取消
- `disconnect_count -> int`: 获取当前断开检测次数

### 便捷函数

#### create_disconnect_monitor

```python
def create_disconnect_monitor(
    http_request: Request,
    cancellation_token: asyncio.Event,
    is_streaming: bool = False
) -> ClientDisconnectMonitor
```

创建监控器的便捷函数，使用默认参数。

#### with_disconnect_monitoring

```python
def with_disconnect_monitoring(
    http_request: Request,
    is_streaming: bool = False
) -> DisconnectMonitorContext
```

返回上下文管理器，自动管理监控器生命周期。

## 工作原理

1. **监控循环**：在后台运行异步任务，定期检查客户端连接状态
2. **断开检测**：使用 `http_request.is_disconnected()` 检测连接状态
3. **计数机制**：连续检测到断开达到阈值次数后，设置取消令牌
4. **重置机制**：当连接恢复时，重置断开计数器，避免误判
5. **参数区分**：流式和非流式请求使用不同的检测参数

## 最佳实践

1. **选择合适的请求类型**：正确设置 `is_streaming` 参数
2. **及时清理**：确保在请求结束时调用 `stop_monitoring()`
3. **使用上下文管理器**：推荐使用上下文管理器自动管理生命周期
4. **检查取消令牌**：在长时间运行的操作中定期检查 `cancellation_token.is_set()`
5. **异常处理**：在业务逻辑中适当处理 `asyncio.CancelledError`

## 示例：在流式生成器中使用

```python
async def my_stream_generator(cancellation_token: asyncio.Event):
    try:
        for i in range(1000):
            # 检查是否被取消
            if cancellation_token.is_set():
                logger.info("流式生成被取消")
                break
                
            # 生成数据
            data = f"chunk {i}"
            yield f"data: {data}\n\n"
            
            await asyncio.sleep(0.1)
            
        yield "data: [DONE]\n\n"
        
    except asyncio.CancelledError:
        logger.info("流式生成被取消")
        yield "data: [CANCELLED]\n\n"
```

## 注意事项

- 监控器会在后台运行异步任务，确保正确清理以避免资源泄漏
- 断开检测基于 FastAPI 的 `Request.is_disconnected()` 方法
- 在高并发场景下，建议适当调整检测参数以平衡性能和响应性
- 取消令牌是共享的，多个组件可以监听同一个令牌的状态
