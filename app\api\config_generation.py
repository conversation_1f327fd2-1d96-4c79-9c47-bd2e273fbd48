"""
配置生成API路由
实现/generate-config端点，支持流式和非流式响应
"""

import asyncio
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse
from app.services.config_generation.config_generation import config_generation_service
from app.models.request import ConfigGenerationRequest
from app.models.response import ConfigGenerationResponse
from app.utils.logger import get_logger
from app.utils.client_disconnect_monitor import create_disconnect_monitor

# 获取日志记录器
logger = get_logger(__name__)

router = APIRouter()


@router.post("/generate-config")
async def generate_config(request: ConfigGenerationRequest, http_request: Request):
    """生成配置接口"""
    try:
        logger.info(f"收到配置生成请求: {request.dict()}")

        # 创建取消令牌
        cancellation_token = asyncio.Event()

        # 创建客户端断开连接监控器
        disconnect_monitor = create_disconnect_monitor(
            http_request=http_request,
            cancellation_token=cancellation_token,
            is_streaming=request.stream
        )

        # 启动监控任务（流式和非流式都需要）
        disconnect_monitor.start_monitoring()

        try:
            if request.stream:
                print('流式返回')
                # 流式返回
                async def generate_stream():
                    async for chunk in config_generation_service.generate_config_stream(request, cancellation_token):
                        # 检查是否被取消
                        if cancellation_token.is_set():
                            logger.info("流式响应被取消")
                            break
                        yield f"data: {chunk}\n\n"
                    yield "data: [DONE]\n\n"

                return StreamingResponse(
                    generate_stream(),
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Headers": "*",
                    }
                )
            else:
                # 非流式返回
                result = await config_generation_service.generate_config(request, cancellation_token)
                return result
        finally:
            # 清理监控任务
            await disconnect_monitor.stop_monitoring()

    except asyncio.CancelledError:
        logger.info("配置生成请求被取消")
        raise HTTPException(status_code=499, detail="请求被客户端取消")
    except ValueError as e:
        logger.error(f"请求参数错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"配置生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail="配置生成失败")
