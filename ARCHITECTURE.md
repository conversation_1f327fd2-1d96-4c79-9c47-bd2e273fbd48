# 系统架构设计文档

## 概述

数通设备配置生成智能体采用分层架构设计，通过清晰的职责分离和模块化设计，实现了高可扩展性和可维护性。

## 整体架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        API 层 (FastAPI)                        │
├─────────────────────────────────────────────────────────────────┤
│                      服务层 (Services)                          │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │  意图识别服务    │    │  配置生成服务    │                    │
│  └─────────────────┘    └─────────────────┘                    │
├─────────────────────────────────────────────────────────────────┤
│                      核心层 (Core)                              │
│  ┌─────────────────┐    ┌─────────────────────────────────────┐ │
│  │  能力路由器      │    │         四大核心能力                │ │
│  │                │    │  ┌─────────┐  ┌─────────┐           │ │
│  │                │    │  │身份识别 │  │通用能力 │           │ │
│  │                │    │  └─────────┘  └─────────┘           │ │
│  │                │    │  ┌─────────┐  ┌─────────┐           │ │
│  │                │    │  │开放式   │  │定制化   │           │ │
│  │                │    │  └─────────┘  └─────────┘           │ │
│  └─────────────────┘    └─────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                      基础层 (Base)                              │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │  基础能力类      │    │  LLM基础类      │                    │
│  └─────────────────┘    └─────────────────┘                    │
├─────────────────────────────────────────────────────────────────┤
│                      工具层 (Utils)                             │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐              │
│  │LLM客户端│ │知识库   │ │文本转换 │ │日志管理 │              │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘              │
└─────────────────────────────────────────────────────────────────┘
```

## 核心组件设计

### 1. 基础能力类 (BaseCapability)

**职责**: 提供所有能力的通用功能
**核心方法**:
- `process()`: 处理请求的主入口
- `_validate_request()`: 请求验证
- `_format_response()`: 响应格式化

**设计模式**: 模板方法模式

```python
class BaseCapability:
    async def process(self, request: ConfigGenerationRequest) -> ConfigGenerationResponse:
        # 模板方法，定义处理流程
        self._validate_request(request)
        content = await self._generate_content(request)
        return self._format_response(content, request)
    
    async def _generate_content(self, request) -> str:
        # 抽象方法，由子类实现
        raise NotImplementedError
```

### 2. LLM基础类 (LLMBasedCapability)

**职责**: 处理与大语言模型的交互
**核心功能**:
- LLM客户端管理
- 流式和非流式调用
- 参数标准化
- 错误处理和重试

**继承关系**: 继承自BaseCapability

```python
class LLMBasedCapability(BaseCapability):
    def __init__(self):
        self.llm_client = LLMClient()
    
    async def _call_llm(self, messages, stream=False):
        # 统一的LLM调用接口
        pass
    
    def _get_llm_params(self, request):
        # 获取LLM调用参数
        pass
```

### 3. 能力路由器 (CapabilityRouter)

**职责**: 根据意图识别结果，将请求路由到对应的能力模块
**核心功能**:
- 能力注册和管理
- 智能路由分发
- 异常处理

**设计模式**: 策略模式 + 工厂模式

```python
class CapabilityRouter:
    def __init__(self):
        self.capabilities = {
            "identity": IdentityCapability(),
            "general": GeneralCapability(),
            "open": OpenCapability(),
            "customized": CustomizedCapability()
        }
    
    async def route(self, intent_type: str, request: ConfigGenerationRequest):
        capability = self.capabilities.get(intent_type)
        if not capability:
            raise ValueError(f"Unsupported intent type: {intent_type}")
        return await capability.process(request)
```

## 数据流设计

### 请求处理流程

```
用户请求 → FastAPI → 配置生成服务 → 意图识别服务 → 能力路由器 → 具体能力模块 → 响应
    ↓         ↓           ↓              ↓              ↓           ↓
  JSON     参数验证    请求解析      LLM调用        路由分发     内容生成
  解析     异常处理    消息提取      意图分析        能力选择     格式化输出
```

### 数据模型设计

**请求模型**:
```python
class ConfigGenerationRequest(BaseModel):
    messages: List[Message]
    device_type: Optional[str] = None
    vendor: Optional[str] = None
    stream: bool = False
```

**响应模型**:
```python
class ConfigGenerationResponse(BaseModel):
    success: bool
    message: str
    code: int
    config_content: str
    intent_type: Optional[str] = None
    device_type: Optional[str] = None
    vendor: Optional[str] = None
```

## 扩展性设计

### 1. 新增能力模块

要添加新的能力模块，只需：

1. 继承BaseCapability或LLMBasedCapability
2. 实现_generate_content方法
3. 在CapabilityRouter中注册

```python
class NewCapability(LLMBasedCapability):
    async def _generate_content(self, request: ConfigGenerationRequest) -> str:
        # 实现具体的内容生成逻辑
        pass

# 在路由器中注册
router.capabilities["new_type"] = NewCapability()
```

### 2. 新增意图类型

1. 在意图识别服务中添加新的意图识别逻辑
2. 创建对应的能力模块
3. 在路由器中注册映射关系

### 3. 新增厂商支持

1. 添加厂商特定的模板文件
2. 在常量定义中添加厂商标识
3. 在能力模块中添加厂商特定逻辑

## 性能优化设计

### 1. 异步处理
- 全链路异步设计
- 非阻塞I/O操作
- 并发请求处理

### 2. 缓存策略
- 意图识别结果缓存
- 知识库查询结果缓存
- 模板编译结果缓存

### 3. 连接池管理
- HTTP连接池复用
- 数据库连接池
- LLM客户端连接池

## 安全性设计

### 1. 输入验证
- 请求参数验证
- 文件上传限制
- SQL注入防护

### 2. 访问控制
- API密钥验证
- 请求频率限制
- IP白名单机制

### 3. 数据保护
- 敏感信息脱敏
- 日志安全处理
- 传输加密

## 监控和运维

### 1. 日志设计
- 结构化日志
- 分级日志记录
- 日志轮转和归档

### 2. 指标监控
- 请求响应时间
- 成功率统计
- 资源使用情况

### 3. 健康检查
- 服务健康状态
- 依赖服务检查
- 自动故障恢复

## 测试策略

### 1. 单元测试
- 各模块功能测试
- 边界条件测试
- 异常情况测试

### 2. 集成测试
- API接口测试
- 服务间交互测试
- 端到端测试

### 3. 性能测试
- 并发压力测试
- 内存泄漏测试
- 长时间运行测试

## 部署架构

### 1. 单机部署
```
┌─────────────────┐
│   Nginx         │ (反向代理)
├─────────────────┤
│   FastAPI App   │ (应用服务)
├─────────────────┤
│   File System   │ (模板存储)
└─────────────────┘
```

### 2. 分布式部署
```
┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   API Gateway   │
├─────────────────┤    ├─────────────────┤
│   App Instance1 │    │   App Instance2 │
├─────────────────┤    ├─────────────────┤
│   Redis Cache   │    │   File Storage  │
└─────────────────┘    └─────────────────┘
```

这种架构设计确保了系统的高可用性、可扩展性和可维护性，为未来的功能扩展和性能优化提供了坚实的基础。
