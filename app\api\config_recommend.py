"""
配置推荐API路由
实现/config-recommend端点，支持流式和非流式响应
"""

import json
import time
import asyncio
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse
from loguru import logger

from app.models.request import ConfigRecommendRequest
from app.models.response import ConfigRecommendResponse
from app.core.config_recommend_capability import config_recommend_capability
from app.utils.device_mapping import device_mapping_service
from app.utils.client_disconnect_monitor import create_disconnect_monitor


router = APIRouter()


@router.post("/config-recommend")
async def config_recommend(request: ConfigRecommendRequest, http_request: Request):
    """
    配置推荐接口

    支持流式和非流式响应，兼容OpenAI API格式
    """
    try:
        logger.info(f"收到配置推荐请求: stream={request.stream}")

        # 创建取消令牌
        cancellation_token = asyncio.Event()

        # 创建客户端断开连接监控器
        disconnect_monitor = create_disconnect_monitor(
            http_request=http_request,
            cancellation_token=cancellation_token,
            is_streaming=request.stream
        )

        # 启动监控任务（流式和非流式都需要）
        disconnect_monitor.start_monitoring()

        try:
            if request.stream:
                # 流式响应
                return StreamingResponse(
                    _stream_config_recommend(request, cancellation_token),
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "Content-Type": "text/event-stream; charset=utf-8"
                    }
                )
            else:
                # 非流式响应
                response = await config_recommend_capability.generate_config(request, cancellation_token)
                return response
        finally:
            # 清理监控任务
            await disconnect_monitor.stop_monitoring()

    except asyncio.CancelledError:
        logger.info("配置推荐请求被取消")
        raise HTTPException(status_code=499, detail="请求被客户端取消")
    except Exception as e:
        logger.error(f"配置推荐接口错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"配置推荐失败: {str(e)}")


async def _stream_config_recommend(request: ConfigRecommendRequest, cancellation_token: asyncio.Event):
    """流式配置推荐生成器"""
    try:
        async for chunk in config_recommend_capability.generate_config_stream(request, cancellation_token):
            # 检查是否被取消
            if cancellation_token.is_set():
                logger.info("流式响应被取消")
                break

            # 格式化为SSE格式
            chunk_data = json.dumps(chunk, ensure_ascii=False)
            yield f"data: {chunk_data}\n\n"
            
        # 发送结束标记
        yield "data: [DONE]\n\n"
        logger.info('流式配置推荐完成')

    except asyncio.CancelledError:
        logger.info("流式配置推荐被取消")
        # 发送取消信息
        cancel_chunk = {
            "id": f"chatcmpl-cancelled",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": "config-recommend",
            "choices": [{
                "index": 0,
                "delta": {
                    "content": "请求已被取消"
                },
                "finish_reason": "cancelled"
            }]
        }
        cancel_data = json.dumps(cancel_chunk, ensure_ascii=False)
        yield f"data: {cancel_data}\n\n"
        yield "data: [DONE]\n\n"
    except Exception as e:
        logger.error(f"流式配置推荐生成错误: {str(e)}")
        # 发送错误信息
        error_chunk = {
            "id": f"chatcmpl-error",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": "config-recommend",
            "choices": [{
                "index": 0,
                "delta": {
                    "content": f"生成配置推荐时发生错误: {str(e)}"
                },
                "finish_reason": "error"
            }]
        }
        error_data = json.dumps(error_chunk, ensure_ascii=False)
        yield f"data: {error_data}\n\n"
        yield "data: [DONE]\n\n"


@router.get("/config-recommend/devices")
async def get_supported_devices():
    """获取支持的设备列表"""
    try:
        devices = device_mapping_service.get_supported_devices()
        
        return {
            "success": True,
            "data": devices,
            "message": f"获取到{len(devices)}个支持的设备"
        }
        
    except Exception as e:
        logger.error(f"获取支持设备列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取支持设备列表失败: {str(e)}")


@router.get("/config-recommend/milvus/info")
async def get_milvus_info():
    """获取Milvus连接信息"""
    try:
        from app.utils.milvus_client import milvus_client
        
        info = {
            "connected": milvus_client.connected,
            "host": milvus_client.host,
            "port": milvus_client.port,
            "collection_name": milvus_client.collection_name
        }
        
        return {
            "success": True,
            "data": info,
            "message": "获取Milvus信息成功"
        }
        
    except Exception as e:
        logger.error(f"获取Milvus信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取Milvus信息失败: {str(e)}")


@router.post("/config-recommend/milvus/test")
async def test_milvus_search():
    """测试Milvus搜索功能"""
    try:
        from app.utils.milvus_client import milvus_client
        
        # 测试搜索
        results = await milvus_client.hybrid_search(
            query_text="BGP配置",
            knowledge_base_id="test_kb",
            search_params={"top_k": 3}
        )
        
        return {
            "success": True,
            "data": {
                "results_count": len(results),
                "results": results
            },
            "message": "Milvus搜索测试成功"
        }
        
    except Exception as e:
        logger.error(f"Milvus搜索测试失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Milvus搜索测试失败: {str(e)}")
