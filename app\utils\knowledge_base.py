"""
知识库客户端工具
"""

import json
from typing import Dict, Any, List, Optional
import httpx
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential

from app.config.settings import settings


class KnowledgeBaseClient:
    """知识库客户端类"""
    
    def __init__(self):
        self.base_url = settings.kb_base_url
        self.app_id = settings.kb_app_id
        self.category_id = settings.kb_category_id
        self.top_k = settings.kb_top_k
        self.similarity = settings.kb_similarity
        self.timeout = settings.kb_timeout
        
        self.headers = {
            "Content-Type": "application/json"
        }
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=8)
    )
    async def change_category_id(self,category_id:int):
        self.category_id = category_id;

    async def _make_request(self, payload: Dict[str, Any]) -> httpx.Response:
        """发起HTTP请求"""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(
                self.base_url,
                headers=self.headers,
                json=payload
            )
            response.raise_for_status()
            return response
    
    async def search(
        self,
        query: str,
        top_k: Optional[int] = None,
        similarity: Optional[float] = None,
        category_id: Optional[int] = None,
        knowledge_base_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """搜索知识库"""
        payload = {
            "appId": self.app_id,
            "topK": top_k or self.top_k,
            "categoryId": category_id or self.category_id,
            "query": query,
            "similarity": similarity or self.similarity
        }

        # 如果指定了知识库ID，添加到请求中
        if knowledge_base_id:
            payload["knowledgeBaseId"] = knowledge_base_id
            logger.info(f"使用指定知识库: {knowledge_base_id}")
        
        try:
            logger.info(f"发送知识库搜索请求: {json.dumps(payload, ensure_ascii=False)}")
            response = await self._make_request(payload)
            result = response.json()
            logger.info(f"知识库搜索响应: {json.dumps(result, ensure_ascii=False)}")
            
            
            # 提取搜索结果
            if isinstance(result, dict) and "data" in result:
                
                return result["data"]["segments"]
            elif isinstance(result, list):
                return result
            else:
                logger.warning(f"未知的知识库响应格式: {result}")
                return []
                
        except httpx.HTTPStatusError as e:
            logger.error(f"知识库搜索HTTP错误: {e.response.status_code} - {e.response.text}")
            raise
        except httpx.RequestError as e:
            logger.error(f"知识库搜索网络错误: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"知识库搜索未知错误: {str(e)}")
            raise
    
    async def search_by_device_type(
        self,
        query: str,
        device_type: str,
        vendor: str,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """根据设备类型和厂商搜索知识库"""
        # 构建增强查询
        enhanced_query = f"{device_type} {vendor} {query}"
        return await self.search(enhanced_query, **kwargs)
    
    def format_search_results(self, results: List[Dict[str, Any]]) -> str:
        """格式化搜索结果为文本"""
        if not results:
            return "未找到相关文档"
        
        formatted_docs = []
        for i, doc in enumerate(results, 1):
            # 根据实际的知识库响应格式调整字段名
            content = doc.get("content", doc.get("text", doc.get("document", "")))
            title = doc.get("title", doc.get("name", f"文档{i}"))
            score = doc.get("score", doc.get("similarity", 0))
            
            formatted_doc = f"文档{i}: {title}\n内容: {content}\n相似度: {score:.3f}\n"
            formatted_docs.append(formatted_doc)
        
        return "\n".join(formatted_docs)
    
    async def get_reference_docs(
        self,
        query: str,
        device_type: str = "",
        vendor: str = "",
        max_docs: int = 3
    ) -> str:
        """获取参考文档的便捷方法"""
        try:
            if device_type and vendor:
                results = await self.search_by_device_type(
                    query, device_type, vendor, top_k=max_docs
                )
            else:
                results = await self.search(query, top_k=max_docs)
            
            return self.format_search_results(results)
            
        except Exception as e:
            logger.error(f"获取参考文档失败: {str(e)}")
            return "获取参考文档失败，请稍后重试"


# 全局知识库客户端实例
kb_client = KnowledgeBaseClient()
