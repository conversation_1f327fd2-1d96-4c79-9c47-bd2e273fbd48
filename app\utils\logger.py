"""
日志配置工具
"""

import sys
import os
from pathlib import Path
from loguru import logger

from app.config.settings import settings


def setup_logger():
    """配置日志系统"""
    # 移除默认的日志处理器
    logger.remove()
    
    # 确保日志目录存在
    log_dir = Path(settings.log_dir)
    log_dir.mkdir(exist_ok=True)
    
    # 控制台日志格式
    console_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 文件日志格式
    file_format = (
        "{time:YYYY-MM-DD HH:mm:ss} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )
    
    # 添加控制台处理器
    logger.add(
        sys.stdout,
        format=console_format,
        level="INFO" if not settings.debug else "DEBUG",
        colorize=True
    )
    
    # 添加文件处理器 - 普通日志
    logger.add(
        log_dir / "app.log",
        format=file_format,
        level="INFO",
        rotation="1 day",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    # 添加文件处理器 - 错误日志
    logger.add(
        log_dir / "error.log",
        format=file_format,
        level="ERROR",
        rotation="1 day",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    # 添加文件处理器 - 调试日志（仅在调试模式下）
    if settings.debug:
        logger.add(
            log_dir / "debug.log",
            format=file_format,
            level="DEBUG",
            rotation="1 day",
            retention="7 days",
            compression="zip",
            encoding="utf-8"
        )
    
    logger.info("日志系统初始化完成")


def get_logger(name: str = None):
    """获取日志记录器"""
    if name:
        return logger.bind(name=name)
    return logger


# 初始化日志系统
setup_logger()
