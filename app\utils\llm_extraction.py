"""
LLM信息抽取工具
使用LLM从用户问题中抽取厂商、设备、型号信息和问题改写
"""

import json
import re
from typing import Dict, Any, Optional, Tuple
from loguru import logger
from app.utils.llm_client import llm_client


class LLMExtractionService:
    """LLM信息抽取服务类"""
    
    def __init__(self):
        # 信息抽取提示词模板
        self.extraction_prompt_template = """你是一个网络设备信息抽取专家。请从用户的问题中抽取设备信息，并将问题改写为更适合知识库检索的形式。

用户问题：{user_question}

请分析并提取以下信息：
1. 厂商：华为/思科/华三/瞻博等，如果没有明确提及则返回null
2. 设备类型：路由器/交换机/防火墙等，如果没有明确提及则返回null
3. 型号：具体的设备型号，如果没有明确提及则返回null
4. 问题改写：将用户问题改写为更适合技术文档检索的专业术语，保持原意但使用标准的技术词汇

改写原则：
- 使用标准的网络技术术语
- 突出配置相关的关键词
- 保持问题的核心意图
- 适合在技术文档中检索

请以JSON格式返回结果：
{{
    "vendor": "厂商名称或null",
    "device": "设备类型或null",
    "model": "设备型号或null",
    "original_question": "用户原始问题",
    "rewritten_question": "改写后的问题"
}}

只返回JSON，不要其他内容。"""

        # 意图识别提示词模板
        self.intent_prompt_template = """你是一个专业的意图识别助手。请判断用户的问题属于以下哪种意图类型：

1. identity - 询问助手身份、功能介绍、你是谁等
2. config_recommend - 询问设备配置推荐、配置方法、配置步骤、如何配置等
3. invalid - 与网络设备配置无关的问题（如天气、娱乐、生活等）

用户问题：{user_question}

请仅返回意图类型（identity/config_recommend/invalid），不要包含其他内容。"""

    async def extract_device_info_and_rewrite(
        self, 
        user_question: str
    ) -> Dict[str, Any]:
        """从用户问题中抽取设备信息并改写问题"""
        try:
            prompt = self.extraction_prompt_template.format(user_question=user_question)
            
            response = await llm_client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                stream=False,
                temperature=0.1  # 使用较低温度确保结果稳定
            )
            
            if "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"].strip()
                
                # 尝试解析JSON响应
                try:
                    # 清理可能的markdown格式
                    if content.startswith("```json"):
                        content = content[7:]
                    if content.endswith("```"):
                        content = content[:-3]
                    if content.startswith("<think>"):
                        pattern = r'<think>.*?</think>'
                        content = re.sub(pattern, '', content, flags=re.DOTALL)
                    
                    result = json.loads(content)
                    logger.info(f"LLM信息抽取结果: {result}")
                    
                    # 标准化结果
                    return {
                        "vendor": result.get("vendor") if result.get("vendor") != "null" else None,
                        "device": result.get("device") if result.get("device") != "null" else None,
                        "model": result.get("model") if result.get("model") != "null" else None,
                        "original_question": result.get("original_question", user_question),
                        "rewritten_question": result.get("rewritten_question", user_question)
                    }
                    
                except json.JSONDecodeError as e:
                    logger.error(f"LLM响应JSON解析失败: {content}, 错误: {e}")
                    return self._fallback_extraction(user_question)
            else:
                logger.error("LLM响应格式不正确")
                return self._fallback_extraction(user_question)
                
        except Exception as e:
            logger.error(f"LLM信息抽取失败: {str(e)}")
            return self._fallback_extraction(user_question)

    async def recognize_intent(self, user_question: str) -> str:
        """使用LLM识别用户意图"""
        try:
            prompt = self.intent_prompt_template.format(user_question=user_question)

            response = await llm_client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                stream=False,
                temperature=0.1
            )

            if "choices" in response and len(response["choices"]) > 0:
                intent = response["choices"][0]["message"]["content"].strip().lower()

                # 验证返回的意图类型
                valid_intents = ["identity", "config_recommend", "invalid"]
                if intent in valid_intents:
                    logger.info(f"LLM意图识别结果: {intent}")
                    return intent
                else:
                    logger.warning(f"LLM返回了无效的意图类型: {intent}")
                    return self._fallback_intent_recognition(user_question)
            else:
                logger.error("LLM意图识别响应格式不正确")
                return self._fallback_intent_recognition(user_question)

        except Exception as e:
            logger.error(f"LLM意图识别失败: {str(e)}")
            return self._fallback_intent_recognition(user_question)

    def _fallback_intent_recognition(self, user_question: str) -> str:
        """当LLM意图识别失败时的备用方法"""
        try:
            text_lower = user_question.lower()

            # 身份识别关键词
            identity_keywords = ["你是谁", "你是什么", "介绍", "功能", "能力", "作用"]
            if any(keyword in text_lower for keyword in identity_keywords):
                return "identity"

            # 配置推荐关键词
            config_keywords = ["配置", "设置", "如何", "怎么", "方法", "步骤", "推荐", "建议"]
            device_keywords = ["路由器", "交换机", "防火墙", "router", "switch", "firewall"]
            protocol_keywords = ["bgp", "ospf", "vlan", "acl", "nat", "dhcp"]

            if (any(keyword in text_lower for keyword in config_keywords) and
                (any(keyword in text_lower for keyword in device_keywords) or
                 any(keyword in text_lower for keyword in protocol_keywords))):
                return "config_recommend"

            # 默认为无效意图
            return "invalid"

        except Exception as e:
            logger.error(f"备用意图识别失败: {str(e)}")
            return "invalid"
    
    def _fallback_extraction(self, user_question: str) -> Dict[str, Any]:
        """当LLM抽取失败时的备用方法"""
        try:
            # 简单的关键词匹配作为备用方案
            text_lower = user_question.lower()
            
            # 厂商识别
            vendor = None
            vendor_keywords = {
                "华为": ["华为", "huawei", "hw"],
                "思科": ["思科", "cisco"],
                "华三": ["华三", "h3c"],
                "瞻博": ["瞻博", "juniper"]
            }
            
            for v, keywords in vendor_keywords.items():
                if any(keyword in text_lower for keyword in keywords):
                    vendor = v
                    break
            
            # 设备类型识别
            device = None
            device_keywords = {
                "路由器": ["路由器", "router", "路由"],
                "交换机": ["交换机", "switch", "交换"],
                "防火墙": ["防火墙", "firewall", "防火"]
            }
            
            for d, keywords in device_keywords.items():
                if any(keyword in text_lower for keyword in keywords):
                    device = d
                    break
            
            # 型号识别（简单匹配）
            model = None
            model_patterns = [
                r'ne\d+[a-z]*',  # 华为NE系列
                r'ar\d+',        # 华为AR系列
                r'ce\d+',        # 华为CE系列
                r'isr\d+',       # 思科ISR系列
                r'asr\d+',       # 思科ASR系列
                r'msr\d+',       # 华三MSR系列
            ]
            
            for pattern in model_patterns:
                match = re.search(pattern, text_lower)
                if match:
                    model = match.group().upper()
                    break
            
            # 简单的问题改写
            rewritten_question = user_question
            config_keywords = ["配置", "设置", "如何", "怎么", "方法", "步骤"]
            if any(keyword in user_question for keyword in config_keywords):
                # 如果包含配置相关词汇，保持原样
                rewritten_question = user_question
            else:
                # 否则添加配置相关词汇
                rewritten_question = f"{user_question} 配置方法"
            
            return {
                "vendor": vendor,
                "device": device,
                "model": model,
                "original_question": user_question,
                "rewritten_question": rewritten_question
            }
            
        except Exception as e:
            logger.error(f"备用信息抽取失败: {str(e)}")
            return {
                "vendor": None,
                "device": None,
                "model": None,
                "original_question": user_question,
                "rewritten_question": user_question
            }
    
    async def extract_from_conversation(
        self, 
        messages: list
    ) -> Dict[str, Any]:
        """从对话消息中抽取信息"""
        try:
            # 获取最新的用户消息
            user_message = None
            for message in reversed(messages):
                if hasattr(message, 'role') and message.role == "user":
                    user_message = message.content
                    break
            
            if not user_message:
                return {
                    "vendor": None,
                    "device": None,
                    "model": None,
                    "original_question": "",
                    "rewritten_question": ""
                }
            
            return await self.extract_device_info_and_rewrite(user_message)
            
        except Exception as e:
            logger.error(f"从对话中抽取信息失败: {str(e)}")
            return {
                "vendor": None,
                "device": None,
                "model": None,
                "original_question": "",
                "rewritten_question": ""
            }


# 全局LLM信息抽取服务实例
llm_extraction_service = LLMExtractionService()
