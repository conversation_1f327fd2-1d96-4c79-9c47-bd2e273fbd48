"""
身份识别能力模块

处理用户询问助手身份、功能等问题的核心能力
"""

import asyncio
import json
import time
from typing import AsyncGenerator, Dict, Any
from loguru import logger

from app.models.request import ConfigGenerationRequest


class IdentityCapability:
    """身份识别能力处理器"""
    
    def __init__(self):
        self.identity_response = """我是云池数通配置生成助手，专门为网络工程师提供智能化的数通设备配置生成服务。

我的主要能力包括：

🔧 **通用配置能力**
- 支持华为、H3C、中兴等主流厂商设备
- 涵盖路由器、交换机、防火墙等设备类型
- 提供BGP、OSPF、VLAN、ACL等协议配置

📊 **定制化配置能力** 
- 基于Excel表格数据批量生成配置
- 支持CE端BGP、VLAN、端口等参数化配置
- 自动识别表格结构并生成对应配置

📖 **开放式学习能力**
- 基于示例配置学习生成新配置
- 支持模板参考和配置模仿
- 智能理解配置模式和规律

💬 **多轮对话支持**
- 支持连续对话和上下文理解
- 可以基于之前的配置进行修改和优化
- 提供配置解释和故障排查建议

🚀 **实时流式响应**
- 快速响应用户请求
- 实时生成配置内容
- 支持大型配置的分块传输

我致力于帮助网络工程师提高配置效率，减少人工错误，让网络配置变得更加智能和便捷！

有什么网络配置问题，随时可以问我哦～"""

    async def generate_config(self, request: ConfigGenerationRequest) -> str:
        """生成身份识别响应"""
        logger.info("开始生成身份识别响应")

        # 模拟一点处理时间，保持与其他能力的一致性
        await asyncio.sleep(0.1)

        logger.info("身份识别响应生成完成")
        return self.identity_response

    async def generate_config_stream(self, request: ConfigGenerationRequest) -> AsyncGenerator[Dict[str, Any], None]:
        """生成流式身份识别响应（返回标准OpenAI格式）"""
        logger.info("开始生成流式身份识别响应")

        # 发送开始块
        start_chunk = {
            "id": f"chatcmpl-start",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": "identity-capability",
            "choices": [
                {
                    "index": 0,
                    "delta": {
                        "role": "assistant"
                    },
                    "finish_reason": None
                }
            ]
        }
        yield start_chunk
        await asyncio.sleep(0.1)

        # 发送意图识别块
        intent_chunk = {
            "id": f"chatcmpl-intent",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": "identity-capability",
            "choices": [
                {
                    "index": 0,
                    "delta": {
                        "content": "正在为您介绍我的身份和能力..."
                    },
                    "finish_reason": None
                }
            ]
        }
        yield intent_chunk
        await asyncio.sleep(0.1)

        # 将响应分成多个块进行流式传输
        lines = self.identity_response.split('\n')
        current_chunk = ""

        for i, line in enumerate(lines):
            current_chunk += line + '\n'

            # 每3行或者遇到空行时发送一个块
            if (i + 1) % 3 == 0 or line.strip() == "" or i == len(lines) - 1:
                if current_chunk.strip():
                    content_chunk = {
                        "id": f"chatcmpl-content-{i}",
                        "object": "chat.completion.chunk",
                        "created": int(time.time()),
                        "model": "identity-capability",
                        "choices": [
                            {
                                "index": 0,
                                "delta": {
                                    "content": current_chunk
                                },
                                "finish_reason": None
                            }
                        ]
                    }
                    yield content_chunk
                    current_chunk = ""
                    await asyncio.sleep(0.05)  # 小延迟模拟流式传输

        # 发送结束块
        end_chunk = {
            "id": f"chatcmpl-end",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": "identity-capability",
            "choices": [
                {
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }
            ]
        }
        yield end_chunk

        logger.info("流式身份识别响应生成完成")


# 创建全局实例
identity_capability = IdentityCapability()
