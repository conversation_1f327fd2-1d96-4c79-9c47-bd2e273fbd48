# 附件字段功能说明

## 概述

为了解决customize场景下附件处理过于粗糙的问题，我们在Message模型中新增了`attachment`字段，用于精确分离用户文本内容和附件内容。

## 问题背景

在原有的实现中，文件上传后的内容会被直接添加到`content`字段中，这导致：

1. **数据混合**：用户的文本描述和附件数据混在一起
2. **解析错误**：文本解析可能出现格式错误
3. **处理困难**：难以精确区分哪些是附件内容，哪些是用户问答内容

## 解决方案

### 新的数据结构

```json
{
  "messages": [
    {
      "role": "user",
      "content": "用户的文本描述和需求",
      "attachment": [
        {"列1": "值1", "列2": "值2"},
        {"列1": "值3", "列2": "值4"}
      ]
    }
  ]
}
```

### attachment字段类型

- **Excel文件**：`List[Dict[str, Any]]` - 解析后的结构化数据
- **TXT文件**：`str` - 纯文本内容
- **无附件**：`null` - 向后兼容

## 功能特性

### 1. 数据分离清晰

```python
message = Message(
    role="user",
    content="请根据这个Excel表格生成BGP配置",  # 用户需求
    attachment=[  # 纯净的Excel数据
        {"BGP AS号": "65001", "设备名": "Router1"},
        {"BGP AS号": "65002", "设备名": "Router2"}
    ]
)
```

### 2. 类型安全

- Excel数据自动解析为结构化格式
- TXT数据保持字符串格式
- 类型明确，减少处理错误

### 3. 精准处理

- **customize场景**：直接使用attachment中的结构化数据，与Jinja2模板结合生成配置
- **open场景**：分别处理示例和需求数据
- **general场景**：主要使用content，attachment作为补充

### 4. 向后兼容

- 现有的content解析逻辑保持不变
- attachment字段为可选字段
- 优先使用attachment，降级到content

## API使用方式

### 1. 直接JSON请求

```bash
curl -X POST 'http://localhost:8000/generate-config' \
  -H 'Content-Type: application/json' \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "请根据这个Excel表格生成BGP配置",
        "attachment": [
          {"BGP AS号": "65001", "设备名": "Router1", "接口": "GE0/0/1"},
          {"BGP AS号": "65002", "设备名": "Router2", "接口": "GE0/0/2"}
        ]
      }
    ],
    "device_type": "router",
    "vendor": "huawei"
  }'
```

### 2. TXT附件示例

```bash
curl -X POST 'http://localhost:8000/generate-config' \
  -H 'Content-Type: application/json' \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "请参考这个配置文件生成类似的配置",
        "attachment": "interface GE0/0/1\n ip address *********** *************\n undo shutdown"
      }
    ],
    "device_type": "router",
    "vendor": "huawei"
  }'
```

## 实现细节

### 1. Message模型扩展

```python
class Message(BaseModel):
    role: str = Field(..., description="消息角色：user, assistant, system")
    content: str = Field(..., description="消息内容")
    attachment: Optional[Union[List[Dict[str, Any]], str]] = Field(
        default=None, 
        description="附件内容：Excel解析后为List[Dict]，TXT文件为str"
    )
```

### 2. 数据提取优化

各个能力模块都优化了数据提取逻辑：

- **CustomizedCapability**：优先从attachment获取结构化数据，直接用于Jinja2模板
- **OpenCapability**：分别处理attachment和content
- **GeneralCapability**：将attachment信息作为补充

### 3. 数据处理流程

1. 接收请求 → 2. 检查attachment字段 → 3. 直接使用结构化数据 → 4. 与模板结合生成配置

## 优势对比

| 方面 | 原有方案 | 新方案 |
|------|----------|--------|
| 数据分离 | 混合在content中 | 清晰分离content和attachment |
| 类型安全 | 字符串解析 | 结构化数据 + 类型明确 |
| 错误率 | 解析错误较多 | 大幅减少解析错误 |
| 处理精度 | 粗糙处理 | 精准定位数据，直接用于模板 |
| 兼容性 | - | 完全向后兼容 |
| 模板集成 | 需要文本解析 | 直接使用结构化数据 |

## 测试验证

运行测试文件验证功能：

```bash
python test_attachment_feature.py
python test_customize_with_attachment.py
python example_usage.py
```

## 注意事项

1. **数据类型**：Excel数据为`List[Dict]`，TXT数据为`str`
2. **支持格式**：主要支持Excel结构化数据和TXT文本数据
3. **向后兼容**：完全兼容现有的content字段处理方式
4. **错误处理**：完善的错误处理和降级机制

## 核心优势

1. **customize场景精准处理**：直接使用结构化数据，避免文本解析错误
2. **与Jinja2模板完美集成**：结构化数据可直接用于模板渲染
3. **数据类型安全**：明确的数据类型，减少运行时错误
4. **处理效率提升**：跳过文本解析步骤，直接使用数据

## 未来扩展

1. 支持更复杂的数据结构
2. 增加数据验证机制
3. 支持多个attachment字段
4. 添加数据转换和预处理功能
