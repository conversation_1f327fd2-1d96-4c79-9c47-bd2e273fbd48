"""
常量定义模块
"""

from enum import Enum


class IntentType(Enum):
    """意图类型枚举"""
    CUSTOMIZED = "customized"  # 定制化能力
    OPEN = "open"  # 开放式能力
    GENERAL = "general"  # 通用能力
    IDENTITY = "identity"  # 身份识别能力
    B_MVSP_NMS = "b_mvsp_nms"  # 承载B网MVSP_NMS配置能力
    ANOTHER = "another"  # 需求分析能力
    CONFIG_RECOMMEND = "config_recommend"  # 配置推荐能力
    INVALID = "invalid"  # 无效意图
    CUSTOMIZEDSCENE2 = "customized_scene_2"  # 定制化场景2


class DeviceType(Enum):
    """设备类型枚举"""
    ROUTER = "router"  # 路由器
    SWITCH = "switch"  # 交换机
    FIREWALL = "firewall"  # 防火墙
    UNKNOWN = "unknown"  # 未知设备


class VendorType(Enum):
    """厂商类型枚举"""
    HUAWEI = "huawei"  # 华为
    CISCO = "cisco"  # 思科
    H3C = "h3c"  # 华三
    JUNIPER = "juniper"  # 瞻博
    UNKNOWN = "unknown"  # 未知厂商


# 定制化能力表头关键词
CUSTOMIZED_TABLE_HEADERS = [
    "CE端BGP AS号", "CE端设备", "设备型号", "CE端口", "CE端互联IP地址",
    "VLAN", "BFD时延", "vpn-instance", "rt/rd", "接收地址范围",
    "发送地址范围", "AS号", "终端设备", "终端端口", "ER互联IP地址",
    "互联带宽", "VRF name", "vpn申请人", "联系方式", "备注"
]

CUSTOMIZED_SCENE_2_TABLE_HEADERS= [
    "匹配对应前缀列表", "新申请IP地址段"
]
# B_MVSP_NMS能力关键词
B_MVSP_NMS_KEYWORDS = [
    "承载B网", "MVSP_NMS", "mvsp_nms", "接收地址", "发送地址",
    "地址范围", "ip-prefix", "pl-MVSP_NMS", "调整", "新增", "删除",
    "原接收地址", "新增接收地址", "回收", "undo"
]

# 设备配置无关问题的回复
INVALID_QUESTION_RESPONSE = "对不起，请提问与配置生成相关的问题"

# 配置推荐相关常量
CONFIG_RECOMMEND_KEYWORDS = [
    "配置推荐", "推荐配置", "如何配置", "配置方法", "配置步骤",
    "怎么配置", "配置指导", "配置建议", "配置方案"
]

# 身份相关关键词
IDENTITY_KEYWORDS = [
    "你是谁", "你是什么", "介绍一下", "你的功能", "你能做什么",
    "助手介绍", "功能介绍", "能力介绍"
]

# 配置推荐身份回复
CONFIG_RECOMMEND_IDENTITY_RESPONSE = """我是云池数通设备配置推荐助手，负责对数通设备进行配置步骤推荐。

我的主要功能包括：
1. 根据您的问题，智能推荐相应的设备配置步骤
2. 支持华为、华三、中兴等主流厂商设备
3. 基于权威技术文档和最佳实践提供配置建议
4. 支持多轮对话，可以针对您的具体需求进行深入交流

请告诉我您需要配置的设备类型、厂商和具体需求，我将为您提供专业的配置推荐。"""

# 默认知识库ID
DEFAULT_KNOWLEDGE_BASE_ID = "default_kb"

# 多轮对话默认保存轮数
DEFAULT_CONVERSATION_ROUNDS = 3

# 文件类型
SUPPORTED_FILE_TYPES = {
    ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ".xls": "application/vnd.ms-excel",
    ".txt": "text/plain",
    ".csv": "text/csv"
}

# HTTP状态码
HTTP_STATUS = {
    "SUCCESS": 200,
    "BAD_REQUEST": 400,
    "UNAUTHORIZED": 401,
    "FORBIDDEN": 403,
    "NOT_FOUND": 404,
    "INTERNAL_ERROR": 500,
    "SERVICE_UNAVAILABLE": 503
}

# 日志级别
LOG_LEVELS = {
    "DEBUG": "DEBUG",
    "INFO": "INFO",
    "WARNING": "WARNING",
    "ERROR": "ERROR",
    "CRITICAL": "CRITICAL"
}

# 提示词模板
PROMPT_TEMPLATES = {
    "OPEN_CAPABILITY": """你是数通设备配置助手，用户上传了excel：{excel_content}，txt：{txt_content}。
数通设备配置会通过excel的内容生成txt的结果，请学习之后模仿该方式，根据实际的需求excel：{requirement_excel}，返回配置代码。
设备类型：{device_type}，厂商：{vendor}""",
    
    "GENERAL_CAPABILITY": """你是数通设备配置助手，请根据用户的问题和参考文档生成配置代码。
用户问题：{user_question}
参考文档：{reference_docs}
设备类型：{device_type}，厂商：{vendor}
如果用户的问题比较简单，你在生成配置代码时，根据参考文档只生成必要的内容，但是可以给予用户一些可选的配置选项。以下是一个例子：
####例子
用户问题是：请配置一个以太网汇聚接口配置。
回答：interface Eth-Trunk1
 description "To-[PEER-DEVICE]-eth-trunk1"  # 描述对端设备信息
 mode lacp-static  # 配置为LACP静态模式
 portswitch  # 默认为二层接口，如需三层接口则使用"undo portswitch"
 
# 可选配置（根据实际需求添加）：
#  peer-link 1  # 如果作为M-LAG对等链路则启用
#  stp edged-port enable  # 启用STP边缘端口功能
#  dfs-group 1 m-lag 1  # 加入DFS组和M-LAG
#  port nvo3 mode access  # 配置为NVO3接入模式
#  ip address *********** *************  # 如果是三层接口则配置IP地址
""",

    "ANOTHER_CAPABILITY":"""
    你是通信网络配置需求分析助手，可以从用户意图描述中分析出问用户提问的数据对应领域(config_field)、需求背景(background)和sub_query。
    要求是：
    config_field：枚举值只有路由器、交换机、防火墙以及其他
        config_field不同专业补充说明：
        路由器：核心功能是在不同网络之间（如LAN到WAN）转发数据包，专注于IP层（第3层）的路由决策。关键能力包括支持动态路由协议（如OSPF, BGP, RIP）、处理NAT（网络地址转换）、VPN（虚拟专用网）、QoS（服务质量）、管理WAN连接和子网划分。常见术语有：路由表、IP地址、网关、BGP邻居、WAN接口、路由策略。典型应用场景是企业广域网连接、互联网接入、多地点网络整合。识别线索：当用户输入中出现“路由”、“BGP”、“WAN”、“NAT”或“网关”等关键词时，优先考虑路由器。
        交换机：核心功能是在局域网（LAN）内连接设备，基于MAC地址转发数据帧，主要工作在数据链路层（第2层），部分高级交换机支持第3层。关键能力包括实现MAC地址学习、VLAN（虚拟局域网）划分、STP（生成树协议）防环、提供端口管理、广播域控制和链路聚合，以及PoE（以太网供电）。常见术语有：MAC地址、VLAN、交换机端口、STP、帧转发、PoE。典型应用场景是企业内部网络扩展、设备互联、流量隔离（如在办公室或数据中心）。识别线索：当用户输入中出现“交换机”、“MAC”、“VLAN”、“端口配置”或“生成树”等关键词时，优先考虑交换机。
        防火墙：核心功能是作为网络安全设备，监控和控制进出网络的流量，提供访问控制和威胁防护，工作在网络层到应用层（第3-7层）。关键能力包括实施包过滤、状态检测、入侵防御系统（IPS）、管理安全策略、VPN隧道、应用层控制（如Web过滤）和日志审计，以及设置DMZ（隔离区）。常见术语有：防火墙规则、ACL（访问控制列表）、安全策略、入侵检测、VPN隧道、DMZ。典型应用场景是网络安全防护、网络边界防御、合规审计。识别线索：当用户输入中出现“防火墙”、“安全策略”、“ACL”、“入侵检测”或“VPN安全”等关键词时，优先考虑防火墙。
        其他：核心范围涵盖路由器、交换机、防火墙之外的所有网络通信专业或更广泛的IT领域。包括的技术有无线网络（Wi-Fi/AP）、SD-WAN（软件定义广域网）、负载均衡器、云网络服务、物联网（IoT）设备管理、QoS策略优化等。常见术语有：AP（接入点）、SD-WAN、负载均衡、云安全、网络优化、QoS策略。典型应用场景是混合云部署、移动网络优化、综合解决方案设计。识别线索：当用户输入中出现“无线”、“SD-WAN”、“负载均衡”、“云网络”等关键词，或者输入内容明显不匹配前三类专业（路由器、交换机、防火墙）时，归类为其他。当涉及跨领域或新兴技术时也归入此类。
    background：一句话总结，语义完整
    sub_query：一个或多个指令列表，表达简洁包含配置的关键信息
    输出内容格式是json，但不可含有解析、注释、禁止’json‘标识等和结果无关的内容，不要包含任何其他额外的文本、解释或标记。样例：
    {{
      "config_field": "路由器",
      "background": "需求背景",
      "sub_query": ["动作1","动作2",...]
    }}
    """
}

# 正则表达式模式
REGEX_PATTERNS = {
    "IP_ADDRESS": r"\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b",
    "VLAN_ID": r"\bvlan\s+(\d+)\b",
    "AS_NUMBER": r"\bas\s+(\d+)\b",
    "DEVICE_NAME": r"^[a-zA-Z0-9\-_]+$"
}

# 缓存配置
CACHE_CONFIG = {
    "TTL": 3600,  # 缓存过期时间(秒)
    "MAX_SIZE": 1000,  # 最大缓存条目数
    "CLEANUP_INTERVAL": 300  # 清理间隔(秒)
}
