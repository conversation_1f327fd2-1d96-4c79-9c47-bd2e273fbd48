"""
配置推荐核心能力
实现配置推荐的RAG流程，整合意图识别、知识库检索、LLM问答等功能
"""

import json
import time
import asyncio
from typing import Dict, Any, List, AsyncGenerator, Optional
from loguru import logger

from app.core.base_capability import LLMBasedCapability
from app.models.request import ConfigRecommendRequest
from app.models.response import ConfigRecommendResponse
from app.utils.llm_extraction import llm_extraction_service
from app.utils.device_mapping import device_mapping_service
from app.utils.knowledge_base import kb_client
from app.utils.milvus_client import milvus_client

from app.config.constants import (
    CONFIG_RECOMMEND_IDENTITY_RESPONSE, 
    IDENTITY_KEYWORDS,
    CONFIG_RECOMMEND_KEYWORDS
)


class ConfigRecommendCapability(LLMBasedCapability):
    """配置推荐能力类"""
    
    def __init__(self):
        super().__init__("config-recommend")
        
        # 配置推荐提示词模板
        self.recommend_prompt_template = """你是云池数通设备配置推荐助手，负责对数通设备进行配置步骤推荐。

用户问题：{user_question}
设备信息：{device_info}
参考文档：{reference_docs}

请根据用户问题和参考文档，提供专业的配置推荐步骤。

要求：
1. 基于参考文档内容，提供准确可靠的配置建议
2. 配置步骤要清晰、详细、易于理解
3. 如果涉及具体设备，请针对该设备特点进行说明
4. 提供必要的注意事项和最佳实践建议
5. 使用中文回答

请提供详细的配置推荐："""
    
    async def _intent_recognition(self, user_input: str) -> str:
        """使用LLM进行意图识别"""
        try:
            return await llm_extraction_service.recognize_intent(user_input)
        except Exception as e:
            logger.error(f"LLM意图识别失败: {str(e)}")
            # 备用方案：关键词匹配
            user_lower = user_input.lower()

            # 检查身份相关问题
            identity_keywords = ["你是谁", "你是什么", "介绍", "功能", "能力"]
            if any(keyword in user_lower for keyword in identity_keywords):
                return "identity"

            # 检查是否与配置相关
            config_keywords = ["配置", "设置", "如何", "怎么", "方法", "步骤", "推荐"]
            tech_keywords = [
                "bgp", "ospf", "isis", "vlan", "interface", "route", "ip",
                "协议", "网络", "路由", "交换", "防火墙"
            ]

            if (any(keyword in user_lower for keyword in config_keywords) or
                any(keyword in user_lower for keyword in tech_keywords)):
                return "config_recommend"
            else:
                return "invalid"
    
    async def _extract_device_info(
        self,
        messages: List[Dict[str, Any]],
        user_input: str
    ) -> Dict[str, Any]:
        """从消息中提取设备信息"""
        try:
            # 从最新的用户消息中提取设备信息
            vendor = None
            device = None
            model = None

            # 查找最新的用户消息中的设备信息
            for message in reversed(messages):
                # 处理Pydantic模型
                if hasattr(message, 'role'):
                    if message.role == "user":
                        vendor = vendor or getattr(message, 'vendor', None)
                        device = device or getattr(message, 'device', None)
                        model = model or getattr(message, 'model', None)
                # 处理字典
                elif isinstance(message, dict):
                    if message.get("role") == "user":
                        vendor = vendor or message.get("vendor")
                        device = device or message.get("device")
                        model = model or message.get("model")

                # 如果找到了完整信息就停止
                if vendor and device and model:
                    break

            # 如果消息中没有指定，使用LLM抽取
            if not vendor or not device or not model:
                extracted_info = await llm_extraction_service.extract_device_info_and_rewrite(user_input)

                vendor = vendor or extracted_info.get("vendor")
                device = device or extracted_info.get("device")
                model = model or extracted_info.get("model")

                return {
                    "vendor": vendor or "未知",
                    "device": device or "未知",
                    "model": model or "未知",
                    "original_question": extracted_info.get("original_question", user_input),
                    "rewritten_question": extracted_info.get("rewritten_question", user_input)
                }
            else:
                return {
                    "vendor": vendor or "未知",
                    "device": device or "未知",
                    "model": model or "未知",
                    "original_question": user_input,
                    "rewritten_question": user_input
                }
                
        except Exception as e:
            logger.error(f"提取设备信息失败: {str(e)}")
            return {
                "vendor": vendor or "未知",
                "device": device or "未知",
                "model": model or "未知",
                "original_question": user_input,
                "rewritten_question": user_input
            }
    
    async def _search_knowledge_base(
        self,
        query: str,
        knowledge_base_id: str,
        request: ConfigRecommendRequest
    ) -> List[Dict[str, Any]]:
        """搜索知识库"""
        try:
            if request.isDirectMilvus:
                # 使用Milvus直连搜索
                logger.info("使用Milvus直连搜索")
                search_params = request.milvusSearchParams or {}
                results = await milvus_client.hybrid_search(
                    query_text=query,
                    knowledge_base_id=knowledge_base_id,
                    search_params=search_params
                )
            else:
                # 使用现有的知识库接口，支持指定知识库ID
                logger.info(f"使用知识库接口搜索，知识库ID: {knowledge_base_id}")
                results = await kb_client.search(
                    query=query,
                    knowledge_base_id=knowledge_base_id
                )
            
            logger.info(f"知识库搜索完成，返回{len(results)}个结果")
            return results
            
        except Exception as e:
            logger.error(f"知识库搜索失败: {str(e)}")
            return []
    
    async def _format_reference_docs(self, search_results: List[Dict[str, Any]]) -> str:
        """格式化参考文档"""
        if not search_results:
            return "未找到相关参考文档"
        
        formatted_docs = []
        for i, doc in enumerate(search_results, 1):
            content = doc.get("content", doc.get("text", ""))
            title = doc.get("title", f"文档{i}")
            score = doc.get("score", doc.get("similarity", 0))
            
            formatted_doc = f"参考文档{i}: {title}\n内容: {content}\n相关度: {score:.3f}\n"
            formatted_docs.append(formatted_doc)
        
        return "\n".join(formatted_docs)
    
    async def generate_config(self, request: ConfigRecommendRequest, cancellation_token: Optional[asyncio.Event] = None) -> ConfigRecommendResponse:
        """生成配置推荐（非流式）"""
        try:
            # 获取用户输入
            user_input = ""
            if request.messages:
                for message in reversed(request.messages):
                    if hasattr(message, 'role') and message.role == "user":
                        user_input = message.content
                        break
            
            if not user_input:
                return ConfigRecommendResponse(
                    success=False,
                    message="未找到有效的用户输入",
                    code=400
                )
            
            # 检查是否被取消
            if cancellation_token and cancellation_token.is_set():
                logger.info("配置推荐生成被取消")
                raise asyncio.CancelledError("配置推荐生成被取消")

            # 意图识别
            intent = await self._intent_recognition(user_input)

            # 检查是否被取消
            if cancellation_token and cancellation_token.is_set():
                logger.info("配置推荐生成被取消")
                raise asyncio.CancelledError("配置推荐生成被取消")

            # 处理身份识别
            if intent == "identity":
                return ConfigRecommendResponse(
                    success=True,
                    message="身份介绍",
                    code=200,
                    config_content=CONFIG_RECOMMEND_IDENTITY_RESPONSE,
                    intent_type="identity"
                )
            
            # 处理无效意图
            if intent == "invalid":
                return ConfigRecommendResponse(
                    success=False,
                    message="我只回答关于云池数通设备配置推荐的内容",
                    code=400,
                    intent_type="invalid"
                )
            
            # 检查是否被取消
            if cancellation_token and cancellation_token.is_set():
                logger.info("配置推荐生成被取消")
                raise asyncio.CancelledError("配置推荐生成被取消")

            # 提取设备信息
            device_info = await self._extract_device_info(request.messages, user_input)

            # 检查是否被取消
            if cancellation_token and cancellation_token.is_set():
                logger.info("配置推荐生成被取消")
                raise asyncio.CancelledError("配置推荐生成被取消")

            # 获取知识库ID
            knowledge_base_id = device_mapping_service.get_knowledge_base_id(
                device_info.get("vendor"),
                device_info.get("device"),
                device_info.get("model")
            )

            # 检查是否被取消
            if cancellation_token and cancellation_token.is_set():
                logger.info("配置推荐生成被取消")
                raise asyncio.CancelledError("配置推荐生成被取消")

            # 搜索知识库
            search_results = await self._search_knowledge_base(
                device_info.get("rewritten_question", user_input),
                knowledge_base_id,
                request
            )

            # 检查是否被取消
            if cancellation_token and cancellation_token.is_set():
                logger.info("配置推荐生成被取消")
                raise asyncio.CancelledError("配置推荐生成被取消")

            # 格式化参考文档
            reference_docs = await self._format_reference_docs(search_results)

            # 构建LLM提示词
            prompt = self.recommend_prompt_template.format(
                user_question=device_info.get("original_question", user_input),
                device_info=f"厂商: {device_info.get('vendor', '未知')}, 设备: {device_info.get('device', '未知')}, 型号: {device_info.get('model', '未知')}",
                reference_docs=reference_docs
            )
            
            # 调用LLM生成配置推荐
            response = await self._call_llm([{"role": "user", "content": prompt}], cancellation_token=cancellation_token)
            
            if "choices" in response and len(response["choices"]) > 0:
                config_content = response["choices"][0]["message"]["content"]
                
                return ConfigRecommendResponse(
                    success=True,
                    message="配置推荐生成成功",
                    code=200,
                    config_content=config_content,
                    intent_type="config_recommend",
                    device_info={
                        "vendor": device_info.get("vendor"),
                        "device": device_info.get("device"),
                        "model": device_info.get("model")
                    },
                    knowledge_base_id=knowledge_base_id,
                    extracted_info=device_info
                )
            else:
                return ConfigRecommendResponse(
                    success=False,
                    message="LLM响应格式错误",
                    code=500
                )
                
        except Exception as e:
            logger.error(f"配置推荐生成失败: {str(e)}")
            return ConfigRecommendResponse(
                success=False,
                message=f"配置推荐生成失败: {str(e)}",
                code=500
            )

    async def generate_config_stream(self, request: ConfigRecommendRequest, cancellation_token: Optional[asyncio.Event] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """生成配置推荐（流式）"""
        try:
            # 获取用户输入
            user_input = ""
            if request.messages:
                for message in reversed(request.messages):
                    if hasattr(message, 'role') and message.role == "user":
                        user_input = message.content
                        break

            if not user_input:
                yield self._create_error_chunk("未找到有效的用户输入")
                return

            # 发送开始处理消息
            yield self._create_chunk("正在分析您的配置需求...")

            # 意图识别
            intent = await self._intent_recognition(user_input)

            # 处理身份识别
            if intent == "identity":
                yield self._create_chunk(CONFIG_RECOMMEND_IDENTITY_RESPONSE, finish_reason="stop")
                return

            # 处理无效意图
            if intent == "invalid":
                yield self._create_chunk("我只回答关于云池数通设备配置推荐的内容", finish_reason="stop")
                return

            # 发送设备信息提取消息
            yield self._create_chunk("\n\n正在提取设备信息...")

            # 提取设备信息
            device_info = await self._extract_device_info(request.messages, user_input)

            # 发送设备信息
            device_msg = f"\n\n识别的设备信息：\n厂商: {device_info.get('vendor', '未知')}\n设备: {device_info.get('device', '未知')}\n型号: {device_info.get('model', '未知')}"
            yield self._create_chunk(device_msg)

            # 获取知识库ID
            knowledge_base_id = device_mapping_service.get_knowledge_base_id(
                device_info.get("vendor"),
                device_info.get("device"),
                device_info.get("model")
            )

            # 发送知识库搜索消息
            yield self._create_chunk(f"\n\n正在搜索相关技术文档（知识库ID: {knowledge_base_id}）...")

            # 搜索知识库
            search_results = await self._search_knowledge_base(
                device_info.get("rewritten_question", user_input),
                knowledge_base_id,
                request
            )

            # 发送搜索结果消息
            yield self._create_chunk(f"\n\n找到 {len(search_results)} 个相关文档，正在生成配置推荐...")

            # 格式化参考文档
            reference_docs = await self._format_reference_docs(search_results)

            # 构建LLM提示词
            prompt = self.recommend_prompt_template.format(
                user_question=device_info.get("original_question", user_input),
                device_info=f"厂商: {device_info.get('vendor', '未知')}, 设备: {device_info.get('device', '未知')}, 型号: {device_info.get('model', '未知')}",
                reference_docs=reference_docs
            )

            # 发送配置生成开始消息
            yield self._create_chunk("\n\n=== 配置推荐 ===\n\n")

            # 调用LLM流式生成配置推荐
            async for chunk in self._call_llm_stream([{"role": "user", "content": prompt}], cancellation_token=cancellation_token):
                # 检查是否被取消
                if cancellation_token and cancellation_token.is_set():
                    logger.info("LLM流式生成被取消")
                    break
                yield chunk

        except Exception as e:
            logger.error(f"配置推荐流式生成失败: {str(e)}")
            yield self._create_error_chunk(f"配置推荐生成失败: {str(e)}")

    def _create_chunk(self, content: str, finish_reason: Optional[str] = None) -> Dict[str, Any]:
        """创建流式响应块"""
        return {
            "id": f"chatcmpl-{int(time.time())}",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": "config-recommend",
            "choices": [
                {
                    "index": 0,
                    "delta": {"content": content},
                    "finish_reason": finish_reason
                }
            ]
        }

    def _create_error_chunk(self, error_message: str) -> Dict[str, Any]:
        """创建错误响应块"""
        return {
            "id": f"chatcmpl-error-{int(time.time())}",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": "config-recommend",
            "choices": [
                {
                    "index": 0,
                    "delta": {"content": error_message},
                    "finish_reason": "stop"
                }
            ]
        }


# 全局配置推荐能力实例
config_recommend_capability = ConfigRecommendCapability()
