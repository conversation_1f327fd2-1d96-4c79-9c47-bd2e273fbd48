"""
开放式能力模块
基于用户自定义Excel+TXT作为fewshot的配置生成
"""
import json
from typing import Dict, Any, AsyncGenerator
import re
from loguru import logger

from app.config.constants import PROMPT_TEMPLATES
from app.models.request import AnotherCapabilityRequest
from app.utils.knowledge_base import kb_client
from app.utils.llm_client import llm_client
from app.core.base_capability import LLMBasedCapability
import requests


class AnotherCapability(LLMBasedCapability):
    """RAG召回能力类"""

    def __init__(self):
        super().__init__("another-capability")
        self.prompt_template = PROMPT_TEMPLATES["ANOTHER_CAPABILITY"]

    def _extract_content_from_messages(self, messages: list) -> str:
        """从消息列表中提取用户输入内容"""
        # 从后往前查找最新的用户消息
        for message in reversed(messages):
            if message.role == "user":
                # 优先从attachment字段获取附件内容
                if hasattr(message, 'attachment') and message.attachment is not None:
                    # 如果attachment是字符串（TXT文件），直接返回
                    if isinstance(message.attachment, str):
                        return message.attachment.strip()
                    # 如果attachment是列表（Excel解析后的数据），转换为文本格式
                    elif isinstance(message.attachment, list):
                        return self._convert_attachment_data_to_text(message.attachment)

                # 向后兼容：如果没有attachment字段，从content获取
                return message.content.strip()
        return ""

    def _convert_attachment_data_to_text(self, attachment_data: list) -> str:
        """将附件数据转换为文本格式"""
        if not attachment_data:
            return ""

        try:
            # 如果是Excel数据（字典列表），转换为表格格式
            if isinstance(attachment_data[0], dict):
                headers = list(attachment_data[0].keys())
                text_lines = []

                # 添加表头
                header_line = "|".join(headers)
                text_lines.append(header_line)

                # 添加数据行
                for row in attachment_data:
                    row_values = [str(row.get(header, "")) for header in headers]
                    row_line = "|".join(row_values)
                    text_lines.append(row_line)

                return "\n".join(text_lines)
            else:
                # 其他格式，直接转换为字符串
                return str(attachment_data)

        except Exception as e:
            logger.error(f"附件数据转换为文本失败: {str(e)}")
            return str(attachment_data)

    def _extract_attachment_data_from_messages(self, messages: list) -> Dict[str, Any]:
        """从消息列表中提取附件数据"""
        attachment_data = {
            "example_excel": "",
            "example_txt": "",
            "requirement_excel": "",
            "has_attachment": False
        }

        # 从后往前查找最新的用户消息
        for message in reversed(messages):
            if message.role == "user":
                # 检查是否有attachment字段
                if hasattr(message, 'attachment') and message.attachment is not None:
                    attachment_data["has_attachment"] = True

                    # 如果attachment是字符串（TXT文件）
                    if isinstance(message.attachment, str):
                        attachment_data["example_txt"] = message.attachment
                        attachment_data["requirement_excel"] = message.content.strip()

                    # 如果attachment是列表（Excel解析后的数据）
                    elif isinstance(message.attachment, list):
                        excel_text = self._convert_attachment_data_to_text(message.attachment)
                        attachment_data["example_excel"] = excel_text
                        attachment_data["requirement_excel"] = excel_text

                break

        return attachment_data

    def _parse_another_capability_input(self, user_input: str) -> Dict[str, str]:
        """解析开放式能力的用户输入，提取示例和需求数据"""
        # 这里应该实现解析逻辑，提取示例Excel、示例TXT和需求Excel
        # 简化实现，假设用户输入包含所有信息
        return {
            "example_excel": user_input,  # 简化处理
            "example_txt": "",
            "requirement_excel": user_input
        }

    def _build_prompt(self, attachment_data: Dict[str, Any], user_input: str, device_type: str, vendor: str) -> str:
        """构建提示词"""
        try:
            # 如果有attachment数据，优先使用
            if attachment_data.get("has_attachment", False):
                parsed_content = {
                    "example_excel": attachment_data.get("example_excel", ""),
                    "example_txt": attachment_data.get("example_txt", ""),
                    "requirement_excel": attachment_data.get("requirement_excel", "")
                }
            else:
                # 向后兼容：解析用户输入
                parsed_content = self._parse_another_capability_input(user_input)

            prompt = self.prompt_template.format(
                excel_content=parsed_content.get("example_excel", ""),
                txt_content=parsed_content.get("example_txt", ""),
                requirement_excel=parsed_content.get("requirement_excel", ""),
                device_type=device_type,
                vendor=vendor
            )

            logger.info(f"构建rag问题改写能力提示词，长度: {len(prompt)}")

            return prompt
        except Exception as e:
            logger.info("构建提示词出错", e)

    def _prepare_llm_messages(self, request: AnotherCapabilityRequest) -> list[Dict[str, str]]:
        """准备LLM消息"""
        # 从消息中提取附件数据
        attachment_data = self._extract_attachment_data_from_messages(request.messages)

        # 从消息中提取用户输入
        user_input = self._extract_content_from_messages(request.messages)

        # 构建系统提示词
        system_prompt = self._build_prompt(attachment_data, user_input, request.device_type, request.vendor)

        # 构建完整的消息列表
        messages = [{"role": "system", "content": system_prompt}]

        # 添加用户的对话历史（只添加content，不包含attachment）
        for msg in request.messages:
            messages.append({"role": msg.role, "content": msg.content})

        return messages

    def _get_llm_params(self) -> Dict[str, Any]:
        """获取LLM参数"""
        return {"temperature": 0.3}  # 降低温度以获得更稳定的输出

    def _create_error_chunk(self, error_message: str) -> Dict[str, Any]:
        """创建错误响应块"""
        return {
            "id": "chatcmpl-another-error",
            "object": "chat.completion.chunk",
            "created": 1677652288,
            "model": "another-capability",
            "choices": [
                {
                    "index": 0,
                    "delta": {"content": f"配置生成失败: {error_message}"},
                    "finish_reason": "stop"
                }
            ]
        }

    def _extract_content_from_response(self, response: Dict[str, Any]) -> str:
        """从LLM响应中提取内容"""
        if "choices" in response and len(response["choices"]) > 0:
            content = response["choices"][0]["message"]["content"]
            logger.info("开放式配置生成完成")
            return content
        else:
            raise ValueError("LLM响应格式不正确")

    async def _get_reference_docs(
            self,
            user_question: str,
            device_type: str,
            vendor: str
    ) -> str:
        """获取参考文档"""
        try:
            # 使用知识库客户端搜索相关文档
            reference_docs = await kb_client.get_reference_docs(
                query=user_question,
                device_type=device_type,
                vendor=vendor,
                max_docs=3
            )

            logger.info(f"获取参考文档成功，文档长度: {len(reference_docs)}")
            return reference_docs

        except Exception as e:
            logger.error(f"获取参考文档失败: {str(e)}")
            return "暂时无法获取参考文档，将基于通用知识回答"

    async def generate_config_stream(
            self,
            request: AnotherCapabilityRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成配置（流式返回）"""
        try:
            # 准备LLM请求
            messages = self._prepare_llm_messages(request)
            llm_params = self._get_llm_params()

            response = await llm_client.chat_completion(
                messages=messages,
                stream=False,
                **llm_params
            )

            query_data = self._extract_content_from_response(response)

            # 清理可能的markdown格式
            if query_data.startswith("```json"):
                query_data = query_data[7:]
            if query_data.endswith("```"):
                query_data = query_data[:-3]
            if query_data.startswith("<think>"):
                pattern = r'<think>.*?</think>'
                query_data = re.sub(pattern, '', query_data, flags=re.DOTALL)

            query_data = json.loads(query_data)
            print("query_data",query_data)

            if query_data.get("config_field") == "路由器":
                kb_client.change_category_id(183);
            elif query_data.get("config_field") == "交换机":
                kb_client.change_category_id(153);
            elif query_data.get("config_field") == "防火墙":
                kb_client.change_category_id(127);  # 暂时使用通用rag
            else:
                kb_client.change_category_id(127);

            # 初始化rag片段
            rag_list = []

            background = query_data.get('background')
            for query in query_data.get('sub_query'):
                print("子需求：", background + query)
                rag_result = await kb_client.get_reference_docs(background + query)
            rag_list.append(rag_result)

            rag_text = '\n'.join(rag_list)
            system_prompt_content = f"""
            你是通信网络配置专家，请根据提供的信息，解决用户的问题：
            提供信息：{rag_text}
            """

            self.prompt_template = system_prompt_content
            messages = self._prepare_llm_messages(request)

            # 调用LLM流式接口
            async for chunk in llm_client.chat_completion_stream(
                    messages=messages,
                    **llm_params
            ):
                yield chunk

            logger.info("问题改写完成")


        except requests.exceptions.HTTPError as errh:

            print(f"Http Error occurred: {errh}")

        except requests.exceptions.ConnectionError as errc:

            print(f"Error Connecting: {errc}")

        except requests.exceptions.Timeout as errt:

            print(f"Timeout Error: {errt}")

        except requests.exceptions.RequestException as err:

            print(f"Something went wrong: {err}")

    async def generate_config(self, request: AnotherCapabilityRequest) -> str:
        """生成配置（非流式返回）"""
        try:
            # 准备LLM请求
            messages = self._prepare_llm_messages(request)
            llm_params = self._get_llm_params()

            # 调用LLM接口
            response = await llm_client.chat_completion(
                messages=messages,
                stream=False,
                **llm_params
            )

            query_data = self._extract_content_from_response(response)

            # 清理可能的markdown格式
            if query_data.startswith("```json"):
                query_data = query_data[7:]
            if query_data.endswith("```"):
                query_data = query_data[:-3]
            if query_data.startswith("<think>"):
                pattern = r'<think>.*?</think>'
                query_data = re.sub(pattern, '', query_data, flags=re.DOTALL)

            query_data = json.loads(query_data)

            if query_data.get("config_field") == "路由器":
                kb_client.change_category_id(183);
            elif query_data.get("config_field") == "交换机":
                kb_client.change_category_id(153);
            elif query_data.get("config_field") == "防火墙":
                kb_client.change_category_id(127);  # 暂时使用通用rag
            else:
                kb_client.change_category_id(127);

            # 初始化rag片段
            rag_list = []

            background = query_data.get('background')
            for query in query_data.get('sub_query'):
                print("子需求：", background + query)
                rag_result = await kb_client.get_reference_docs(background + query)
            rag_list.append(rag_result)

            rag_text = '\n'.join(rag_list)
            system_prompt_content = f"""
            你是通信网络配置专家，请根据提供的信息，解决用户的问题：
            提供信息：{rag_text}
            """

            self.prompt_template = system_prompt_content
            messages = self._prepare_llm_messages(request)

            # 调用LLM接口
            response = await llm_client.chat_completion(
                messages=messages,
                stream=False,
                **llm_params
            )

            # 提取生成的内容
            return self._extract_content_from_response(response)

        except Exception as e:
            logger.error(f"问题改写失败: {str(e)}")
            raise

    def _validate_request(self, request: AnotherCapabilityRequest) -> bool:
        """验证请求参数"""
        if not request.example_excel.strip():
            raise ValueError("示例Excel内容不能为空")

        if not request.example_txt.strip():
            raise ValueError("示例TXT内容不能为空")

        if not request.requirement_excel.strip():
            raise ValueError("需求Excel内容不能为空")

        if not request.device_type.strip():
            raise ValueError("设备类型不能为空")

        if not request.vendor.strip():
            raise ValueError("厂商信息不能为空")

        return True

    async def generate_config_with_validation(
            self,
            request: AnotherCapabilityRequest,
            stream: bool = False
    ) -> str | AsyncGenerator[Dict[str, Any], None]:
        """带验证的配置生成"""
        # 验证请求参数
        self._validate_request(request)

        if stream:
            return self.generate_config_stream(request)
        else:
            return await self.generate_config(request)


# 全局开放式能力实例
another_capability = AnotherCapability()
